import React, { useState, useEffect } from 'react';
import { voiceAnalyzer } from '../services/voiceAnalyzer';
import { expressionMapper } from '../services/expressionMapper';
import { eyeTracker } from '../services/eyeTracker';

const EnhancedAvatarControls = ({ 
  enableExpressions, 
  enableEyeTracking, 
  enableVoiceAnalysis,
  onToggleExpressions,
  onToggleEyeTracking,
  onToggleVoiceAnalysis,
  currentExpression,
  onExpressionChange
}) => {
  const [voiceState, setVoiceState] = useState({
    isAnalyzing: false,
    isSpeaking: false,
    currentViseme: 'viseme_PP',
    volumeLevel: 0
  });

  const [eyeState, setEyeState] = useState({
    isBlinking: false,
    gazeDirection: { x: 0, y: 0 },
    currentPattern: 'idle'
  });

  // Update voice state
  useEffect(() => {
    if (enableVoiceAnalysis) {
      const updateVoiceState = () => {
        const state = voiceAnalyzer.getCurrentState();
        setVoiceState(state);
      };

      const interval = setInterval(updateVoiceState, 100);
      return () => clearInterval(interval);
    }
  }, [enableVoiceAnalysis]);

  // Update eye state
  useEffect(() => {
    if (enableEyeTracking) {
      const updateEyeState = () => {
        const state = eyeTracker.getEyeState();
        setEyeState(state);
      };

      const interval = setInterval(updateEyeState, 100);
      return () => clearInterval(interval);
    }
  }, [enableEyeTracking]);

  const expressions = [
    'neutral', 'happy', 'sad', 'angry', 'surprised', 'excited', 'thinking', 'confused'
  ];

  const handleExpressionSelect = (expression) => {
    expressionMapper.setExpression(expression, 0.7);
    if (onExpressionChange) {
      onExpressionChange(expression);
    }
  };

  const handleManualBlink = () => {
    eyeTracker.triggerBlink();
  };

  const handleGazeDirection = (direction) => {
    const gazeMap = {
      center: [0, 0],
      left: [-0.3, 0],
      right: [0.3, 0],
      up: [0, 0.3],
      down: [0, -0.3]
    };
    
    const [x, y] = gazeMap[direction] || [0, 0];
    eyeTracker.setGazeTarget(x, y);
  };

  return (
    <div className="enhanced-avatar-controls">
      <style>{`
        .enhanced-avatar-controls {
          background: rgba(255, 255, 255, 0.95);
          border-radius: 12px;
          padding: 20px;
          margin: 10px 0;
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .control-section {
          margin-bottom: 20px;
          padding-bottom: 15px;
          border-bottom: 1px solid #e0e0e0;
        }

        .control-section:last-child {
          border-bottom: none;
          margin-bottom: 0;
        }

        .section-title {
          font-size: 16px;
          font-weight: 600;
          color: #333;
          margin-bottom: 12px;
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .toggle-switch {
          position: relative;
          display: inline-block;
          width: 50px;
          height: 24px;
          margin-left: auto;
        }

        .toggle-switch input {
          opacity: 0;
          width: 0;
          height: 0;
        }

        .slider {
          position: absolute;
          cursor: pointer;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: #ccc;
          transition: .4s;
          border-radius: 24px;
        }

        .slider:before {
          position: absolute;
          content: "";
          height: 18px;
          width: 18px;
          left: 3px;
          bottom: 3px;
          background-color: white;
          transition: .4s;
          border-radius: 50%;
        }

        input:checked + .slider {
          background-color: #4CAF50;
        }

        input:checked + .slider:before {
          transform: translateX(26px);
        }

        .expression-grid {
          display: grid;
          grid-template-columns: repeat(4, 1fr);
          gap: 8px;
          margin-top: 10px;
        }

        .expression-btn {
          padding: 8px 12px;
          border: 2px solid #e0e0e0;
          border-radius: 8px;
          background: white;
          cursor: pointer;
          font-size: 12px;
          text-align: center;
          transition: all 0.3s ease;
        }

        .expression-btn:hover {
          border-color: #4CAF50;
          background: #f8fff8;
        }

        .expression-btn.active {
          border-color: #4CAF50;
          background: #4CAF50;
          color: white;
        }

        .status-indicator {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 12px;
          color: #666;
          margin-top: 8px;
        }

        .status-dot {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background: #ccc;
        }

        .status-dot.active {
          background: #4CAF50;
          animation: pulse 2s infinite;
        }

        .status-dot.speaking {
          background: #ff9800;
        }

        @keyframes pulse {
          0% { opacity: 1; }
          50% { opacity: 0.5; }
          100% { opacity: 1; }
        }

        .gaze-controls {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: 4px;
          margin-top: 10px;
          max-width: 120px;
        }

        .gaze-btn {
          padding: 6px;
          border: 1px solid #ddd;
          background: white;
          cursor: pointer;
          font-size: 10px;
          border-radius: 4px;
          transition: all 0.2s ease;
        }

        .gaze-btn:hover {
          background: #f0f0f0;
        }

        .volume-bar {
          width: 100%;
          height: 4px;
          background: #e0e0e0;
          border-radius: 2px;
          overflow: hidden;
          margin-top: 8px;
        }

        .volume-fill {
          height: 100%;
          background: linear-gradient(90deg, #4CAF50, #ff9800, #f44336);
          transition: width 0.1s ease;
        }
      `}</style>

      {/* Facial Expressions */}
      <div className="control-section">
        <div className="section-title">
          😊 Facial Expressions
          <label className="toggle-switch">
            <input 
              type="checkbox" 
              checked={enableExpressions}
              onChange={(e) => onToggleExpressions(e.target.checked)}
            />
            <span className="slider"></span>
          </label>
        </div>
        
        {enableExpressions && (
          <>
            <div className="expression-grid">
              {expressions.map(expr => (
                <button
                  key={expr}
                  className={`expression-btn ${currentExpression === expr ? 'active' : ''}`}
                  onClick={() => handleExpressionSelect(expr)}
                >
                  {expr}
                </button>
              ))}
            </div>
            <div className="status-indicator">
              <span className="status-dot active"></span>
              Current: {currentExpression}
            </div>
          </>
        )}
      </div>

      {/* Eye Tracking */}
      <div className="control-section">
        <div className="section-title">
          👁️ Eye Tracking
          <label className="toggle-switch">
            <input 
              type="checkbox" 
              checked={enableEyeTracking}
              onChange={(e) => onToggleEyeTracking(e.target.checked)}
            />
            <span className="slider"></span>
          </label>
        </div>
        
        {enableEyeTracking && (
          <>
            <div className="gaze-controls">
              <div></div>
              <button className="gaze-btn" onClick={() => handleGazeDirection('up')}>↑</button>
              <div></div>
              <button className="gaze-btn" onClick={() => handleGazeDirection('left')}>←</button>
              <button className="gaze-btn" onClick={() => handleGazeDirection('center')}>●</button>
              <button className="gaze-btn" onClick={() => handleGazeDirection('right')}>→</button>
              <div></div>
              <button className="gaze-btn" onClick={() => handleGazeDirection('down')}>↓</button>
              <div></div>
            </div>
            <button 
              onClick={handleManualBlink}
              style={{
                marginTop: '8px',
                padding: '4px 8px',
                border: '1px solid #ddd',
                borderRadius: '4px',
                background: 'white',
                cursor: 'pointer',
                fontSize: '12px'
              }}
            >
              Manual Blink
            </button>
            <div className="status-indicator">
              <span className={`status-dot ${eyeState.isBlinking ? 'active' : ''}`}></span>
              Pattern: {eyeState.currentPattern}
            </div>
          </>
        )}
      </div>

      {/* Voice Analysis */}
      <div className="control-section">
        <div className="section-title">
          🎤 Voice Analysis
          <label className="toggle-switch">
            <input 
              type="checkbox" 
              checked={enableVoiceAnalysis}
              onChange={(e) => onToggleVoiceAnalysis(e.target.checked)}
            />
            <span className="slider"></span>
          </label>
        </div>
        
        {enableVoiceAnalysis && (
          <>
            <div className="status-indicator">
              <span className={`status-dot ${voiceState.isAnalyzing ? 'active' : ''}`}></span>
              {voiceState.isAnalyzing ? 'Listening...' : 'Inactive'}
            </div>
            <div className="status-indicator">
              <span className={`status-dot ${voiceState.isSpeaking ? 'speaking' : ''}`}></span>
              Viseme: {voiceState.currentViseme}
            </div>
            <div className="volume-bar">
              <div 
                className="volume-fill" 
                style={{ width: `${voiceState.volumeLevel * 100}%` }}
              ></div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default EnhancedAvatarControls;
