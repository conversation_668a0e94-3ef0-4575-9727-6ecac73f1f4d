import { Canvas } from "@react-three/fiber";
import { Experience } from "./components/Experience";
import Chatbot from "./components/Chatbot";
import AvatarSelection from "./components/AvatarSelection";
import PixarUpload from "./components/PixarUpload";
import React, { useRef, useState, useEffect } from "react";
import CONFIG from "./config";
import "./chatbot.css";

function App() {
  const avatarRef = useRef();
  const [avatarName, setAvatarName] = useState(CONFIG.DEFAULT_AVATAR_NAME);
  const [showNameInput, setShowNameInput] = useState(false);
  const [nameInputValue, setNameInputValue] = useState("");
  const [customAvatar, setCustomAvatar] = useState(null);
  const [reconstructed3DFace, setReconstructed3DFace] = useState(null);
  const [pixarAvatar, setPixarAvatar] = useState(null);

  // Load saved data from localStorage if available
  useEffect(() => {
    const savedName = localStorage.getItem("avatarName");
    if (savedName) {
      setAvatarName(savedName);
    }

    const savedAvatar = localStorage.getItem("customAvatar");
    if (savedAvatar) {
      try {
        setCustomAvatar(JSON.parse(savedAvatar));
      } catch (error) {
        console.error("Error loading saved avatar:", error);
      }
    }

    const savedPixarAvatar = localStorage.getItem("pixarAvatar");
    if (savedPixarAvatar) {
      try {
        setPixarAvatar(JSON.parse(savedPixarAvatar));
      } catch (error) {
        console.error("Error loading saved Pixar avatar:", error);
      }
    }
  }, []);

  // Save name to localStorage when it changes
  useEffect(() => {
    localStorage.setItem("avatarName", avatarName);
  }, [avatarName]);

  // Save custom avatar to localStorage when it changes
  useEffect(() => {
    if (customAvatar) {
      localStorage.setItem("customAvatar", JSON.stringify(customAvatar));
    }
  }, [customAvatar]);

  // Save Pixar avatar to localStorage when it changes
  useEffect(() => {
    if (pixarAvatar) {
      localStorage.setItem("pixarAvatar", JSON.stringify(pixarAvatar));
    }
  }, [pixarAvatar]);

  // Handle name change
  const handleNameChange = (newName) => {
    if (newName && newName.trim() !== "") {
      setAvatarName(newName.trim());
      setShowNameInput(false);
    }
  };

  // Toggle name input visibility
  const toggleNameInput = () => {
    setNameInputValue(avatarName);
    setShowNameInput(!showNameInput);
  };

  // Handle messages from the chatbot
  const handleMessageReceived = (message) => {
    if (avatarRef.current) {
      avatarRef.current.handleNewMessage(message);
    }
  };

  // Handle thinking state
  const handleThinking = () => {
    if (avatarRef.current) {
      avatarRef.current.handleThinking();
    }
  };

  // Handle stop thinking state
  const handleStopThinking = () => {
    if (avatarRef.current) {
      avatarRef.current.handleStopThinking();
    }
  };

  // Handle avatar change
  const handleAvatarChange = (newAvatar) => {
    setCustomAvatar(newAvatar);
    console.log("Avatar changed:", newAvatar);
  };

  // Handle 3D face generation
  const handle3DFaceGenerated = (faceMesh) => {
    setReconstructed3DFace(faceMesh);
    console.log("3D face generated:", faceMesh);
  };

  // Handle Pixar avatar creation
  const handlePixarAvatarCreated = (pixarAvatarData) => {
    setPixarAvatar(pixarAvatarData);
    // Clear other avatar types when Pixar is selected
    setCustomAvatar(null);
    setReconstructed3DFace(null);
    console.log("Pixar avatar created:", pixarAvatarData);
  };



  return (
    <div className="app-container">
      {/* 3D Canvas */}
      <Canvas shadows camera={{ position: [0, 0, 8], fov: 42 }}>
        <color attach="background" args={["#ececec"]} />
        <Experience
          avatarRef={avatarRef}
          customAvatar={pixarAvatar || customAvatar}
          reconstructed3DFace={reconstructed3DFace}
        />
      </Canvas>

      {/* Avatar Name Display */}
      <div className="avatar-name-container">
        <div className="avatar-name" onClick={toggleNameInput}>
          {showNameInput ? (
            <div className="name-input-container">
              <input
                type="text"
                value={nameInputValue}
                onChange={(e) => setNameInputValue(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleNameChange(nameInputValue);
                  }
                }}
                autoFocus
                placeholder="Enter avatar name"
                maxLength={20}
              />
              <button onClick={() => handleNameChange(nameInputValue)}>✓</button>
              <button onClick={() => setShowNameInput(false)}>✕</button>
            </div>
          ) : (
            <>
              <span>{avatarName}</span>
              <button className="edit-name-btn" title="Edit name">✎</button>
            </>
          )}
        </div>
      </div>





      {/* Pixar Upload Component */}
      <PixarUpload onPixarAvatarCreated={handlePixarAvatarCreated} />

      {/* HTML UI overlay */}
      <div className="ui-overlay">
        <Chatbot
          avatarName={avatarName}
          onMessageReceived={handleMessageReceived}
          onThinking={handleThinking}
          onStopThinking={handleStopThinking}
          onNameChange={handleNameChange}
        />
      </div>
    </div>
  );
}

export default App;
