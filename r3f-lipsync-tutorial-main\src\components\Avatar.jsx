/*
Auto-generated by: https://github.com/pmndrs/gltfjsx
Command: npx gltfjsx@6.2.3 public/models/646d9dcdc8a5f5bddbfac913.glb -o src/components/Avatar.jsx -r public
*/

import { useAnimations, useFBX, useGLTF, useTexture } from "@react-three/drei";
import { useFrame, useLoader } from "@react-three/fiber";
import { useControls } from "leva";
import React, { useEffect, useMemo, useRef, useState } from "react";
import * as THREE from "three";
import CONFIG from "../config";
import { face3DReconstructor } from '../services/face3DReconstructor';
import { expressionMapper } from '../services/expressionMapper';
import { eyeTracker } from '../services/eyeTracker';
import { voiceAnalyzer } from '../services/voiceAnalyzer';

const corresponding = {
  A: "viseme_PP",
  B: "viseme_kk",
  C: "viseme_I",
  D: "viseme_AA",
  E: "viseme_O",
  F: "viseme_U",
  G: "viseme_FF",
  H: "viseme_TH",
  X: "viseme_PP",
};

export function Avatar(props) {
  const { customAvatar, reconstructed3DFace, currentLanguage = 'english' } = props;

  const {
    playAudio,
    script,
    headFollow,
    smoothMorphTarget,
    morphTargetSmoothing,
    enableChatbot,
    straightHandsPosition,
    use3DReconstructedFace,
    faceBlendStrength,
    facePositionY,
    facePositionZ,
    faceScale,
    enableExpressions,
    enableEyeTracking,
    enableVoiceAnalysis,
    expressionIntensity,
  } = useControls({
    playAudio: false,
    headFollow: true,
    smoothMorphTarget: true,
    morphTargetSmoothing: 0.5,
    enableChatbot: true,
    straightHandsPosition: true, // Control for keeping hands in a straight position
    use3DReconstructedFace: false, // Toggle for using 3D reconstructed face
    faceBlendStrength: { value: 1.0, min: 0, max: 1, step: 0.1 }, // Blend strength for 3D face
    facePositionY: { value: 1.75, min: 1.0, max: 2.5, step: 0.05 }, // Face Y position
    facePositionZ: { value: 0.15, min: -0.5, max: 0.5, step: 0.01 }, // Face Z position
    faceScale: { value: 0.35, min: 0.1, max: 1.0, step: 0.05 }, // Face scale
    enableExpressions: true, // Enable facial expressions
    enableEyeTracking: true, // Enable eye tracking and blinking
    enableVoiceAnalysis: false, // Enable real-time voice analysis
    expressionIntensity: { value: 0.7, min: 0, max: 1, step: 0.1 }, // Expression intensity
    script: {
      value: "welcome",
      options: ["welcome", "pizzas"],
    },
  });

  // State for chatbot integration
  const [isTalking, setIsTalking] = useState(false);
  const [currentMessage, setCurrentMessage] = useState("");
  const [isThinking, setIsThinking] = useState(false);
  const [chatbotLipsync, setChatbotLipsync] = useState({ mouthCues: [] });
  const [speechStartTime, setSpeechStartTime] = useState(0);

  // State for enhanced features
  const [currentExpression, setCurrentExpression] = useState('neutral');
  const [isVoiceAnalysisActive, setIsVoiceAnalysisActive] = useState(false);
  const [realtimeViseme, setRealtimeViseme] = useState('viseme_PP');
  const [realtimeIntensity, setRealtimeIntensity] = useState(0);

  // Create a speech synthesis instance
  const speechSynthesis = useMemo(() => window.speechSynthesis, []);
  const [voicesLoaded, setVoicesLoaded] = useState(false);

  // Audio for predefined scripts
  const audio = useMemo(() => new Audio(`/audios/${script}.mp3`), [script]);
  const jsonFile = useLoader(THREE.FileLoader, `audios/${script}.json`);
  const lipsync = JSON.parse(jsonFile);

  // Initialize enhanced systems
  useEffect(() => {
    // Initialize expression mapper
    if (enableExpressions) {
      expressionMapper.setExpression('neutral', expressionIntensity);
    }

    // Initialize voice analysis
    if (enableVoiceAnalysis && !isVoiceAnalysisActive) {
      initializeVoiceAnalysis();
    } else if (!enableVoiceAnalysis && isVoiceAnalysisActive) {
      voiceAnalyzer.stopAnalysis();
      setIsVoiceAnalysisActive(false);
    }
  }, [enableExpressions, enableVoiceAnalysis, expressionIntensity]);

  // Initialize speech synthesis voices
  useEffect(() => {
    const loadVoices = () => {
      const voices = speechSynthesis.getVoices();
      if (voices.length > 0) {
        setVoicesLoaded(true);
        console.log('Available voices:', voices.map(v => `${v.name} (${v.lang})`));

        // Log Kannada/Indian voices specifically
        const indianVoices = voices.filter(voice =>
          voice.lang.includes('kn') ||
          voice.lang.includes('hi-IN') ||
          voice.lang.includes('ta-IN') ||
          voice.lang.includes('te-IN') ||
          voice.name.toLowerCase().includes('indian') ||
          voice.name.toLowerCase().includes('kannada')
        );

        if (indianVoices.length > 0) {
          console.log('Indian/Kannada voices found:', indianVoices.map(v => `${v.name} (${v.lang})`));
        } else {
          console.log('No specific Indian/Kannada voices found, will use fallback');
        }
      }
    };

    // Load voices immediately if available
    loadVoices();

    // Also listen for the voiceschanged event
    speechSynthesis.addEventListener('voiceschanged', loadVoices);

    return () => {
      speechSynthesis.removeEventListener('voiceschanged', loadVoices);
    };
  }, [speechSynthesis]);

  // Initialize voice analysis
  const initializeVoiceAnalysis = async () => {
    try {
      const initialized = await voiceAnalyzer.initialize();
      if (initialized) {
        // Set up callbacks
        voiceAnalyzer.setVisemeCallback((viseme, intensity) => {
          setRealtimeViseme(viseme);
          setRealtimeIntensity(intensity);
        });

        voiceAnalyzer.setSpeakingStateCallback((speaking) => {
          eyeTracker.setGazePattern(speaking ? 'speaking' : 'idle');
        });

        voiceAnalyzer.startAnalysis();
        setIsVoiceAnalysisActive(true);
        console.log('Voice analysis initialized and started');
      }
    } catch (error) {
      console.error('Failed to initialize voice analysis:', error);
    }
  };

  // Function to handle new messages from the chatbot
  const handleNewMessage = (message) => {
    setCurrentMessage(message);

    // Analyze sentiment for expressions
    if (enableExpressions) {
      const emotion = expressionMapper.analyzeSentiment(message);
      setCurrentExpression(emotion);
      expressionMapper.setExpression(emotion, expressionIntensity);
    }

    if (CONFIG.VOICE_ENABLED) {
      speakResponse(message);
    }

    // Check if message starts with "Hey there" for greeting animation
    if (message.startsWith("Hey there")) {
      setAnimation("Greeting");
      return;
    }

    // Determine emotion based on message content
    const emotion = analyzeSentiment(message);
    if (emotion === "happy" || emotion === "excited") {
      setAnimation("Greeting");
    } else if (emotion === "sad" || emotion === "angry") {
      setAnimation("Angry");
    } else {
      setAnimation("Idle");
    }
  };

  // Function to generate lipsync data for text
  const generateLipsyncData = (text) => {
    // Estimate speech duration based on text length and speaking rate
    // Average speaking rate is about 150 words per minute, or 2.5 words per second
    // Average word length is about 5 characters
    const estimatedDuration = (text.length / 5) / 2.5;

    // Create a lipsync data structure similar to the JSON files
    const lipsyncData = {
      metadata: {
        duration: estimatedDuration
      },
      mouthCues: []
    };

    // Start with mouth closed
    lipsyncData.mouthCues.push({ start: 0, end: 0.04, value: "X" });

    // Split text into words
    const words = text.split(/\s+/);
    let currentTime = 0.04;

    words.forEach((word, wordIndex) => {
      // Average time per word
      const wordDuration = word.length * 0.07;

      // Process each character in the word
      for (let i = 0; i < word.length; i++) {
        const char = word[i].toLowerCase();
        const charDuration = 0.07; // Average duration per character
        let visemeValue = "B"; // Default viseme

        // Map characters to visemes (using the same mapping as in the JSON files)
        if ("aeiou".includes(char)) {
          // Vowels
          if ("ae".includes(char)) visemeValue = "D";
          else if ("i".includes(char)) visemeValue = "C";
          else if ("o".includes(char)) visemeValue = "E";
          else if ("u".includes(char)) visemeValue = "F";
        } else if ("bmp".includes(char)) {
          // Bilabial consonants
          visemeValue = "A";
        } else if ("fv".includes(char)) {
          // Labiodental consonants
          visemeValue = "G";
        } else if ("th".includes(char)) {
          // Dental consonants
          visemeValue = "H";
        } else {
          // Other consonants
          visemeValue = "B";
        }

        // Add mouth cue
        lipsyncData.mouthCues.push({
          start: currentTime,
          end: currentTime + charDuration,
          value: visemeValue
        });

        currentTime += charDuration;
      }

      // Add a pause between words (except for the last word)
      if (wordIndex < words.length - 1) {
        lipsyncData.mouthCues.push({
          start: currentTime,
          end: currentTime + 0.1,
          value: "X"
        });
        currentTime += 0.1;
      }
    });

    // End with mouth closed
    lipsyncData.mouthCues.push({
      start: currentTime,
      end: currentTime + 0.1,
      value: "X"
    });

    // Update the total duration
    lipsyncData.metadata.duration = currentTime + 0.1;

    return lipsyncData;
  };

  // Function to detect Kannada text
  const isKannadaText = (text) => {
    const kannadaPattern = /[\u0C80-\u0CFF]/;
    return kannadaPattern.test(text);
  };

  // Function to convert Kannada text to phonetic English (comprehensive)
  const kannadaToPhonetic = (text) => {
    console.log('Converting Kannada text:', text);

    // Enhanced Kannada to phonetic mapping with more comprehensive coverage
    const kannadaPhoneticMap = {
      // Greetings and common phrases
      'ನಮಸ್ಕಾರ': 'namaskara',
      'ಹಲೋ': 'hello',
      'ಶುಭೋದಯ': 'shubhodaya',
      'ಶುಭ ಸಂಜೆ': 'shubha sanje',
      'ಶುಭ ರಾತ್ರಿ': 'shubha raatri',
      'ಧನ್ಯವಾದಗಳು': 'dhanyavadagalu',
      'ಕ್ಷಮಿಸಿ': 'kshamissi',
      'ಸರಿ': 'sari',

      // Questions and responses
      'ಹೇಗಿದ್ದೀರಿ': 'hegiddiri',
      'ನಿಮ್ಮ ಹೆಸರೇನು': 'nimma hesarenu',
      'ನನ್ನ ಹೆಸರು': 'nanna hesaru',
      'ಇಂದು ಏನು ದಿನಾಂಕ': 'indu enu dinanka',
      'ಸಮಯ ಎಷ್ಟಾಯಿತು': 'samaya eshtayitu',
      'ನೀವು ಏನು ಮಾಡಬಲ್ಲಿರಿ': 'nivu enu madaballiri',
      'ಈ ಪ್ರಾಜೆಕ್ಟ್ ಬಗ್ಗೆ ಹೇಳಿ': 'ee project bagge heli',

      // Individual common words
      'ನಾನು': 'naanu',
      'ನೀವು': 'neevu',
      'ಅವರು': 'avaru',
      'ಇದು': 'idu',
      'ಅದು': 'adu',
      'ಹೌದು': 'haudu',
      'ಇಲ್ಲ': 'illa',
      'ಚೆನ್ನಾಗಿದೆ': 'chennaagide',
      'ಒಳ್ಳೆಯದು': 'olleyadu',
      'ಸಹಾಯ': 'sahaaya',
      'ಪ್ರಶ್ನೆ': 'prashne',
      'ಉತ್ತರ': 'uttara',
      'ಮಾಹಿತಿ': 'maahiti',
      'ಸಮಯ': 'samaya',
      'ದಿನ': 'dina',
      'ರಾತ್ರಿ': 'raatri',
      'ಬೆಳಿಗ್ಗೆ': 'beligge',
      'ಮಧ್ಯಾಹ್ನ': 'madhyaahna',
      'ಸಂಜೆ': 'sanje',
      'ಬೆಂಗಳೂರು': 'bengaluru',
      'ಕರ್ನಾಟಕ': 'karnataka',
      'ಸಂಸ್ಕೃತಿ': 'samskruti',
      'ಭಾಷೆ': 'bhaashe',
      'ಕನ್ನಡ': 'kannada',
      'ಇಂಗ್ಲಿಷ್': 'english',
      'ಮಾತನಾಡು': 'maatanaadu',
      'ಉತ್ತರಿಸು': 'uttarisu',
      'ಹೇಳಿ': 'heli',
      'ಕೇಳಿ': 'keli',
      'ಬರೆ': 'bare',
      'ಓದು': 'odu',
      'ನೋಡು': 'nodu',
      'ಬಾ': 'baa',
      'ಹೋಗು': 'hogu',
      'ಮಾಡು': 'maadu',
      'ಕೊಡು': 'kodu',
      'ತೆಗೆ': 'tege',
      'ಇರು': 'iru',
      'ಬರು': 'baru'
    };

    let convertedText = text;
    let hasConversions = false;

    // Sort by length (longest first) to avoid partial replacements
    const sortedEntries = Object.entries(kannadaPhoneticMap).sort((a, b) => b[0].length - a[0].length);

    // Replace Kannada words with phonetic equivalents
    for (const [kannada, phonetic] of sortedEntries) {
      if (convertedText.includes(kannada)) {
        convertedText = convertedText.replace(new RegExp(kannada, 'g'), phonetic);
        hasConversions = true;
        console.log(`Converted "${kannada}" to "${phonetic}"`);
      }
    }

    // Clean up any remaining Kannada characters by removing them or replacing with spaces
    if (isKannadaText(convertedText)) {
      console.log('Cleaning remaining Kannada characters...');
      // Replace any remaining Kannada characters with spaces to avoid TTS issues
      convertedText = convertedText.replace(/[\u0C80-\u0CFF]/g, ' ');
      // Clean up multiple spaces
      convertedText = convertedText.replace(/\s+/g, ' ').trim();
      console.log('Cleaned text:', convertedText);
    }

    if (hasConversions || convertedText !== text) {
      console.log('Final phonetic conversion result:', convertedText);
    }

    return convertedText;
  };

  // Helper function to check if a voice is likely male
  const isMaleVoice = (voice) => {
    const maleName = voice.name.toLowerCase();
    const maleKeywords = ['male', 'man', 'boy', 'david', 'mark', 'alex', 'daniel', 'james', 'john', 'michael', 'robert', 'william', 'richard', 'thomas', 'christopher', 'charles', 'matthew', 'anthony', 'donald', 'steven', 'andrew', 'joshua', 'kenneth', 'paul', 'brian', 'kevin', 'george', 'edward', 'ronald', 'timothy', 'jason', 'jeffrey', 'ryan', 'jacob', 'gary', 'nicholas', 'eric', 'jonathan', 'stephen', 'larry', 'justin', 'scott', 'brandon', 'benjamin', 'samuel', 'gregory', 'frank', 'raymond', 'alexander', 'patrick', 'jack', 'dennis', 'jerry', 'tyler', 'aaron', 'jose', 'henry', 'adam', 'douglas', 'nathan', 'peter', 'zachary', 'kyle', 'noah', 'alan', 'ethan', 'jeremy', 'lionel', 'wayne', 'arthur', 'lawrence', 'sean', 'christian', 'harold', 'jordan', 'jesse', 'bryan', 'arthur', 'ralph', 'roy', 'eugene', 'louis', 'philip', 'bobby'];
    const femaleKeywords = ['female', 'woman', 'girl', 'lady', 'mary', 'patricia', 'jennifer', 'linda', 'elizabeth', 'barbara', 'susan', 'jessica', 'sarah', 'karen', 'nancy', 'lisa', 'betty', 'helen', 'sandra', 'donna', 'carol', 'ruth', 'sharon', 'michelle', 'laura', 'sarah', 'kimberly', 'deborah', 'dorothy', 'lisa', 'nancy', 'karen', 'betty', 'helen', 'sandra', 'donna', 'carol', 'ruth', 'sharon', 'michelle', 'laura', 'sarah', 'kimberly', 'deborah', 'dorothy', 'amy', 'angela', 'ashley', 'brenda', 'emma', 'olivia', 'cynthia', 'marie', 'janet', 'catherine', 'frances', 'christine', 'samantha', 'debra', 'rachel', 'carolyn', 'janet', 'virginia', 'maria', 'heather', 'diane', 'julie', 'joyce', 'victoria', 'kelly', 'christina', 'joan', 'evelyn', 'lauren', 'judith', 'megan', 'cheryl', 'andrea', 'hannah', 'jacqueline', 'martha', 'gloria', 'teresa', 'sara', 'janice', 'marie', 'julia', 'heather', 'diane', 'ruth', 'julie', 'joyce', 'virginia'];

    // Check for explicit male/female keywords
    const hasMaleKeyword = maleKeywords.some(keyword => maleName.includes(keyword));
    const hasFemaleKeyword = femaleKeywords.some(keyword => maleName.includes(keyword));

    if (hasMaleKeyword && !hasFemaleKeyword) return true;
    if (hasFemaleKeyword && !hasMaleKeyword) return false;

    // If no clear indication, assume male for Indian/Kannada context (default preference)
    return true;
  };

  // Function to get the default English voice (consistent across languages)
  const getDefaultEnglishVoice = () => {
    if (!voicesLoaded) {
      console.log('Voices not loaded yet, using default');
      return { voice: null, lang: 'en-US' };
    }

    const voices = speechSynthesis.getVoices();

    // Get the default English voice (prefer male for consistency)
    const englishVoices = voices.filter(voice =>
      voice.lang.includes('en-US') ||
      voice.lang.includes('en-GB') ||
      voice.lang.includes('en')
    );

    if (englishVoices.length > 0) {
      const maleEnglishVoices = englishVoices.filter(isMaleVoice);
      const selectedVoice = maleEnglishVoices.length > 0 ? maleEnglishVoices[0] : englishVoices[0];
      return {
        voice: selectedVoice,
        lang: selectedVoice.lang
      };
    }

    return {
      voice: voices[0],
      lang: 'en-US'
    };
  };

  // Function to get available voices for a language
  const getVoiceForLanguage = (language, text) => {
    // Always use the same English voice for consistency
    const defaultVoice = getDefaultEnglishVoice();

    if (language === 'kannada' || isKannadaText(text)) {
      console.log('Using English voice for Kannada with optimized parameters:', defaultVoice.voice?.name || 'default');
      return {
        voice: defaultVoice.voice,
        lang: 'en-US', // Keep English language setting
        isKannadaMode: true // Flag to indicate Kannada content
      };
    }

    // For English, use the same voice with normal parameters
    console.log('Using English voice for English:', defaultVoice.voice?.name || 'default');
    return {
      voice: defaultVoice.voice,
      lang: defaultVoice.lang,
      isKannadaMode: false
    };
  };

  // Simple TTS function for testing
  const simpleTTS = (text) => {
    console.log('🔧 SIMPLE TTS TEST:', text);

    if (speechSynthesis.speaking) {
      speechSynthesis.cancel();
    }

    const utterance = new SpeechSynthesisUtterance(text);
    utterance.lang = 'en-US';
    utterance.rate = 1.0;
    utterance.pitch = 1.0;
    utterance.volume = 1.0;

    utterance.onstart = () => console.log('✅ Simple TTS started');
    utterance.onend = () => console.log('✅ Simple TTS ended');
    utterance.onerror = (e) => console.error('❌ Simple TTS failed:', e);

    speechSynthesis.speak(utterance);
  };

  // Function to speak the response
  const speakResponse = (text) => {
    console.log('=== SPEECH DEBUG ===');
    console.log('Text to speak:', text);
    console.log('Current language:', currentLanguage);
    console.log('Is Kannada text:', isKannadaText(text));
    console.log('Voices loaded:', voicesLoaded);
    console.log('Available voices count:', speechSynthesis.getVoices().length);

    // For Kannada text, try phonetic conversion first
    let textToSpeak = text;
    const isKannada = currentLanguage === 'kannada' || isKannadaText(text);

    if (isKannada) {
      console.log('🔄 Converting Kannada to phonetic...');
      textToSpeak = kannadaToPhonetic(text);
      console.log('Original text:', text);
      console.log('Phonetic text:', textToSpeak);

      // Always proceed with TTS, even if no conversion happened
      // The English TTS will attempt to pronounce Kannada characters
      if (textToSpeak === text) {
        console.log('⚠️ No phonetic conversion available, but proceeding with TTS...');
      } else {
        console.log('✅ Phonetic conversion successful');
      }
    }

    if (speechSynthesis.speaking) {
      console.log('Cancelling previous speech');
      speechSynthesis.cancel();
    }

    // Generate lipsync data for the text
    const lipsyncData = generateLipsyncData(textToSpeak);
    setChatbotLipsync(lipsyncData);

    const utterance = new SpeechSynthesisUtterance(textToSpeak);

    // Always use English settings for consistency
    utterance.lang = 'en-US';

    // Get the default English voice
    const voices = speechSynthesis.getVoices();
    const englishVoices = voices.filter(voice =>
      voice.lang.includes('en-US') || voice.lang.includes('en-GB')
    );

    if (englishVoices.length > 0) {
      utterance.voice = englishVoices[0];
      console.log('Using voice:', englishVoices[0].name);
    }

    // Adjust parameters for Kannada content
    if (isKannada) {
      utterance.rate = 0.85; // Slightly slower for better phonetic pronunciation
      utterance.pitch = 1.05; // Slightly higher pitch but not too much
      utterance.volume = 1.0;
      console.log('Using Kannada-optimized parameters');
    } else {
      utterance.rate = 1.0;
      utterance.pitch = 1.0;
      utterance.volume = 1.0;
      console.log('Using normal English parameters');
    }

    // Add extra pause handling for better pronunciation
    if (isKannada && textToSpeak.length > 50) {
      // For longer texts, add slight pauses after sentences
      textToSpeak = textToSpeak.replace(/\./g, '. ');
      textToSpeak = textToSpeak.replace(/\!/g, '! ');
      textToSpeak = textToSpeak.replace(/\?/g, '? ');
      console.log('Added pauses for longer Kannada text');
    }

    console.log('Final utterance settings:');
    console.log('- Text:', utterance.text);
    console.log('- Lang:', utterance.lang);
    console.log('- Voice:', utterance.voice?.name || 'default');
    console.log('- Rate:', utterance.rate);
    console.log('- Pitch:', utterance.pitch);

    setIsTalking(true);

    // Handle speech events
    utterance.onstart = () => {
      setSpeechStartTime(Date.now());
      console.log('✅ Speech started successfully');
    };

    utterance.onend = () => {
      console.log('✅ Speech ended successfully');
      setIsTalking(false);
      setAnimation("Idle");
    };

    utterance.onerror = (event) => {
      console.error('❌ Speech synthesis error:', event);
      console.error('Error details:', event.error);

      // Try chunked speech for long text
      if (textToSpeak.length > 100) {
        console.log('🔄 Trying chunked speech for long text...');
        setIsTalking(false);
        setAnimation("Idle");

        // Split text into smaller chunks
        const chunks = textToSpeak.match(/.{1,80}(\s|$)/g) || [textToSpeak];
        console.log('Split into chunks:', chunks);

        let chunkIndex = 0;
        const speakChunk = () => {
          if (chunkIndex < chunks.length) {
            const chunk = chunks[chunkIndex].trim();
            console.log(`Speaking chunk ${chunkIndex + 1}/${chunks.length}:`, chunk);

            const chunkUtterance = new SpeechSynthesisUtterance(chunk);
            chunkUtterance.lang = 'en-US';
            chunkUtterance.rate = isKannada ? 0.85 : 1.0;
            chunkUtterance.pitch = isKannada ? 1.05 : 1.0;
            chunkUtterance.volume = 1.0;

            if (englishVoices.length > 0) {
              chunkUtterance.voice = englishVoices[0];
            }

            chunkUtterance.onend = () => {
              chunkIndex++;
              if (chunkIndex < chunks.length) {
                // Small pause between chunks
                setTimeout(speakChunk, 200);
              } else {
                console.log('✅ All chunks completed');
                setIsTalking(false);
                setAnimation("Idle");
              }
            };

            chunkUtterance.onerror = (chunkError) => {
              console.error(`❌ Chunk ${chunkIndex + 1} failed:`, chunkError);
              chunkIndex++;
              if (chunkIndex < chunks.length) {
                setTimeout(speakChunk, 200);
              } else {
                setIsTalking(false);
                setAnimation("Idle");
              }
            };

            if (chunkIndex === 0) {
              setIsTalking(true);
            }

            speechSynthesis.speak(chunkUtterance);
          }
        };

        speakChunk();
        return;
      }

      // For shorter text, try simple fallback
      console.log('🔄 Trying simple TTS fallback...');
      setIsTalking(false);
      setAnimation("Idle");

      setTimeout(() => {
        const fallbackText = isKannada ? 'Namaskara' : 'Hello';
        simpleTTS(fallbackText);
      }, 100);
    };

    console.log('🎤 Attempting to speak...');
    try {
      speechSynthesis.speak(utterance);
      console.log('✅ speechSynthesis.speak() called successfully');
    } catch (error) {
      console.error('❌ Error calling speechSynthesis.speak():', error);
      setIsTalking(false);
      setAnimation("Idle");

      // Try simple fallback
      setTimeout(() => {
        const fallbackText = isKannada ? 'Namaskara' : 'Hello';
        simpleTTS(fallbackText);
      }, 100);
    }
    console.log('=== END SPEECH DEBUG ===');
  };

  // Function to analyze sentiment of text
  const analyzeSentiment = (text) => {
    const positive = /great|happy|excellent|glad|good|wonderful|fantastic/i;
    const negative = /sorry|sad|unfortunate|bad|wrong|error/i;
    const thinking = /think|consider|perhaps|maybe|might|could/i;
    const excited = /excited|amazing|awesome|wow|incredible|love|best/i;
    const surprised = /surprised|unexpected|whoa|really|oh my|no way/i;

    if (excited.test(text)) return "excited";
    if (surprised.test(text)) return "surprised";
    if (positive.test(text)) return "happy";
    if (negative.test(text)) return "sad";
    if (thinking.test(text)) return "thinking";
    return "neutral";
  };

  // Functions to handle thinking state
  const handleThinking = () => {
    setIsThinking(true);
    setAnimation("Idle");
  };

  const handleStopThinking = () => {
    setIsThinking(false);
  };

  useFrame((state, deltaTime) => {
    // Update enhanced systems
    if (enableEyeTracking) {
      eyeTracker.update(deltaTime, isTalking, isThinking);
    }

    if (enableExpressions && reconstructed3DFace && use3DReconstructedFace) {
      expressionMapper.applyExpressionToMesh(reconstructed3DFace, deltaTime);
    }

    // Reset all morph targets for default avatar
    Object.values(corresponding).forEach((value) => {
      if (!smoothMorphTarget) {
        nodes.Wolf3D_Head.morphTargetInfluences[
          nodes.Wolf3D_Head.morphTargetDictionary[value]
        ] = 0;
        nodes.Wolf3D_Teeth.morphTargetInfluences[
          nodes.Wolf3D_Teeth.morphTargetDictionary[value]
        ] = 0;
      } else {
        nodes.Wolf3D_Head.morphTargetInfluences[
          nodes.Wolf3D_Head.morphTargetDictionary[value]
        ] = THREE.MathUtils.lerp(
          nodes.Wolf3D_Head.morphTargetInfluences[
            nodes.Wolf3D_Head.morphTargetDictionary[value]
          ],
          0,
          morphTargetSmoothing
        );

        nodes.Wolf3D_Teeth.morphTargetInfluences[
          nodes.Wolf3D_Teeth.morphTargetDictionary[value]
        ] = THREE.MathUtils.lerp(
          nodes.Wolf3D_Teeth.morphTargetInfluences[
            nodes.Wolf3D_Teeth.morphTargetDictionary[value]
          ],
          0,
          morphTargetSmoothing
        );
      }
    });

    // Reset custom face morphTargets if using 3D reconstructed face
    if (reconstructed3DFace && use3DReconstructedFace && reconstructed3DFace.morphTargetInfluences) {
      // Don't reset if we're applying expressions
      if (!enableExpressions) {
        reconstructed3DFace.morphTargetInfluences.fill(0);
      }
    }

    // Apply eye tracking to custom face
    if (enableEyeTracking && reconstructed3DFace && use3DReconstructedFace) {
      eyeTracker.applyToMesh(reconstructed3DFace);
    }

    // Adjust hand position if straightHandsPosition is enabled
    if (straightHandsPosition) {
      // Adjust arms to be straight at the sides
      // The exact bone names depend on the model's skeleton structure

      // Common bone naming patterns to try
      const leftArmBones = [
        'mixamorigLeftArm', 'LeftArm', 'Left_arm', 'left_arm',
        'mixamorigLeftShoulder', 'LeftShoulder'
      ];

      const rightArmBones = [
        'mixamorigRightArm', 'RightArm', 'Right_arm', 'right_arm',
        'mixamorigRightShoulder', 'RightShoulder'
      ];

      const leftForearmBones = [
        'mixamorigLeftForeArm', 'LeftForeArm', 'Left_forearm', 'left_forearm'
      ];

      const rightForearmBones = [
        'mixamorigRightForeArm', 'RightForeArm', 'Right_forearm', 'right_forearm'
      ];

      // Try to find and adjust left arm
      for (const boneName of leftArmBones) {
        if (nodes[boneName]) {
          nodes[boneName].rotation.z = THREE.MathUtils.lerp(
            nodes[boneName].rotation.z,
            0.1, // Slightly angled for natural position
            0.1  // Smoothing factor
          );
          nodes[boneName].rotation.x = THREE.MathUtils.lerp(
            nodes[boneName].rotation.x,
            0,   // Straight position
            0.1
          );
          break;
        }
      }

      // Try to find and adjust right arm
      for (const boneName of rightArmBones) {
        if (nodes[boneName]) {
          nodes[boneName].rotation.z = THREE.MathUtils.lerp(
            nodes[boneName].rotation.z,
            -0.1, // Slightly angled for natural position (mirrored from left)
            0.1
          );
          nodes[boneName].rotation.x = THREE.MathUtils.lerp(
            nodes[boneName].rotation.x,
            0,    // Straight position
            0.1
          );
          break;
        }
      }

      // Try to find and adjust left forearm
      for (const boneName of leftForearmBones) {
        if (nodes[boneName]) {
          nodes[boneName].rotation.z = THREE.MathUtils.lerp(
            nodes[boneName].rotation.z,
            0,    // Straight position
            0.1
          );
          break;
        }
      }

      // Try to find and adjust right forearm
      for (const boneName of rightForearmBones) {
        if (nodes[boneName]) {
          nodes[boneName].rotation.z = THREE.MathUtils.lerp(
            nodes[boneName].rotation.z,
            0,    // Straight position
            0.1
          );
          break;
        }
      }
    }

    // Handle real-time voice analysis
    if (enableVoiceAnalysis && isVoiceAnalysisActive && realtimeIntensity > 0) {
      const visemeShape = realtimeViseme;

      // Apply to default avatar if not using custom face
      if (!use3DReconstructedFace) {
        if (!smoothMorphTarget) {
          nodes.Wolf3D_Head.morphTargetInfluences[
            nodes.Wolf3D_Head.morphTargetDictionary[visemeShape]
          ] = realtimeIntensity;
          nodes.Wolf3D_Teeth.morphTargetInfluences[
            nodes.Wolf3D_Teeth.morphTargetDictionary[visemeShape]
          ] = realtimeIntensity;
        } else {
          nodes.Wolf3D_Head.morphTargetInfluences[
            nodes.Wolf3D_Head.morphTargetDictionary[visemeShape]
          ] = THREE.MathUtils.lerp(
            nodes.Wolf3D_Head.morphTargetInfluences[
              nodes.Wolf3D_Head.morphTargetDictionary[visemeShape]
            ],
            realtimeIntensity,
            morphTargetSmoothing
          );
          nodes.Wolf3D_Teeth.morphTargetInfluences[
            nodes.Wolf3D_Teeth.morphTargetDictionary[visemeShape]
          ] = THREE.MathUtils.lerp(
            nodes.Wolf3D_Teeth.morphTargetInfluences[
              nodes.Wolf3D_Teeth.morphTargetDictionary[visemeShape]
            ],
            realtimeIntensity,
            morphTargetSmoothing
          );
        }
      }

      // Apply to custom 3D face if available
      if (reconstructed3DFace && use3DReconstructedFace && reconstructed3DFace.morphTargetDictionary) {
        const customIndex = reconstructed3DFace.morphTargetDictionary[visemeShape];
        if (customIndex !== undefined && reconstructed3DFace.morphTargetInfluences) {
          if (!smoothMorphTarget) {
            reconstructed3DFace.morphTargetInfluences[customIndex] = realtimeIntensity;
          } else {
            reconstructed3DFace.morphTargetInfluences[customIndex] = THREE.MathUtils.lerp(
              reconstructed3DFace.morphTargetInfluences[customIndex],
              realtimeIntensity,
              morphTargetSmoothing
            );
          }
        }
      }
    }
    // Handle predefined audio playback
    else if (playAudio && !audio.paused && !audio.ended) {
      const currentAudioTime = audio.currentTime;

      for (let i = 0; i < lipsync.mouthCues.length; i++) {
        const mouthCue = lipsync.mouthCues[i];
        if (
          currentAudioTime >= mouthCue.start &&
          currentAudioTime <= mouthCue.end
        ) {
          const visemeShape = corresponding[mouthCue.value];

          // Apply to default avatar if not using custom face
          if (!use3DReconstructedFace) {
            if (!smoothMorphTarget) {
              nodes.Wolf3D_Head.morphTargetInfluences[
                nodes.Wolf3D_Head.morphTargetDictionary[visemeShape]
              ] = 1;
              nodes.Wolf3D_Teeth.morphTargetInfluences[
                nodes.Wolf3D_Teeth.morphTargetDictionary[visemeShape]
              ] = 1;
            } else {
              nodes.Wolf3D_Head.morphTargetInfluences[
                nodes.Wolf3D_Head.morphTargetDictionary[visemeShape]
              ] = THREE.MathUtils.lerp(
                nodes.Wolf3D_Head.morphTargetInfluences[
                  nodes.Wolf3D_Head.morphTargetDictionary[visemeShape]
                ],
                1,
                morphTargetSmoothing
              );
              nodes.Wolf3D_Teeth.morphTargetInfluences[
                nodes.Wolf3D_Teeth.morphTargetDictionary[visemeShape]
              ] = THREE.MathUtils.lerp(
                nodes.Wolf3D_Teeth.morphTargetInfluences[
                  nodes.Wolf3D_Teeth.morphTargetDictionary[visemeShape]
                ],
                1,
                morphTargetSmoothing
              );
            }
          }

          // Apply to custom 3D face if available
          if (reconstructed3DFace && use3DReconstructedFace && reconstructed3DFace.morphTargetDictionary) {
            const customIndex = reconstructed3DFace.morphTargetDictionary[visemeShape];
            if (customIndex !== undefined && reconstructed3DFace.morphTargetInfluences) {
              if (!smoothMorphTarget) {
                reconstructed3DFace.morphTargetInfluences[customIndex] = 1;
              } else {
                reconstructed3DFace.morphTargetInfluences[customIndex] = THREE.MathUtils.lerp(
                  reconstructed3DFace.morphTargetInfluences[customIndex],
                  1,
                  morphTargetSmoothing
                );
              }
            }
          }
          break;
        }
      }
    }
    // Handle chatbot talking
    else if (isTalking && enableChatbot) {
      // Use the same approach as the predefined audio playback
      const currentTime = (Date.now() - speechStartTime) / 1000;

      // Find the current mouth cue based on the elapsed time
      let currentMouthCue = null;

      for (let i = 0; i < chatbotLipsync.mouthCues.length; i++) {
        const mouthCue = chatbotLipsync.mouthCues[i];
        if (
          currentTime >= mouthCue.start &&
          currentTime <= mouthCue.end
        ) {
          currentMouthCue = mouthCue;
          break;
        }
      }

      // If we found a mouth cue, apply it
      if (currentMouthCue) {
        const visemeShape = corresponding[currentMouthCue.value];

        // Apply to default avatar if not using custom face
        if (!use3DReconstructedFace) {
          if (!smoothMorphTarget) {
            nodes.Wolf3D_Head.morphTargetInfluences[
              nodes.Wolf3D_Head.morphTargetDictionary[visemeShape]
            ] = 1;
            nodes.Wolf3D_Teeth.morphTargetInfluences[
              nodes.Wolf3D_Teeth.morphTargetDictionary[visemeShape]
            ] = 1;
          } else {
            nodes.Wolf3D_Head.morphTargetInfluences[
              nodes.Wolf3D_Head.morphTargetDictionary[visemeShape]
            ] = THREE.MathUtils.lerp(
              nodes.Wolf3D_Head.morphTargetInfluences[
                nodes.Wolf3D_Head.morphTargetDictionary[visemeShape]
              ],
              1,
              morphTargetSmoothing
            );
            nodes.Wolf3D_Teeth.morphTargetInfluences[
              nodes.Wolf3D_Teeth.morphTargetDictionary[visemeShape]
            ] = THREE.MathUtils.lerp(
              nodes.Wolf3D_Teeth.morphTargetInfluences[
                nodes.Wolf3D_Teeth.morphTargetDictionary[visemeShape]
              ],
              1,
              morphTargetSmoothing
            );
          }
        }

        // Apply to custom 3D face if available
        if (reconstructed3DFace && use3DReconstructedFace && reconstructed3DFace.morphTargetDictionary) {
          const customIndex = reconstructed3DFace.morphTargetDictionary[visemeShape];
          if (customIndex !== undefined && reconstructed3DFace.morphTargetInfluences) {
            if (!smoothMorphTarget) {
              reconstructed3DFace.morphTargetInfluences[customIndex] = 1;
            } else {
              reconstructed3DFace.morphTargetInfluences[customIndex] = THREE.MathUtils.lerp(
                reconstructed3DFace.morphTargetInfluences[customIndex],
                1,
                morphTargetSmoothing
              );
            }
          }
        }
      } else {
        // If we're past the end of the lipsync data, use a closed mouth
        const visemeShape = corresponding["X"];

        if (!smoothMorphTarget) {
          nodes.Wolf3D_Head.morphTargetInfluences[
            nodes.Wolf3D_Head.morphTargetDictionary[visemeShape]
          ] = 1;
          nodes.Wolf3D_Teeth.morphTargetInfluences[
            nodes.Wolf3D_Teeth.morphTargetDictionary[visemeShape]
          ] = 1;
        } else {
          nodes.Wolf3D_Head.morphTargetInfluences[
            nodes.Wolf3D_Head.morphTargetDictionary[visemeShape]
          ] = THREE.MathUtils.lerp(
            nodes.Wolf3D_Head.morphTargetInfluences[
              nodes.Wolf3D_Head.morphTargetDictionary[visemeShape]
            ],
            1,
            morphTargetSmoothing
          );
          nodes.Wolf3D_Teeth.morphTargetInfluences[
            nodes.Wolf3D_Teeth.morphTargetDictionary[visemeShape]
          ] = THREE.MathUtils.lerp(
            nodes.Wolf3D_Teeth.morphTargetInfluences[
              nodes.Wolf3D_Teeth.morphTargetDictionary[visemeShape]
            ],
            1,
            morphTargetSmoothing
          );
        }
      }
    }
    // If thinking, show a slight mouth movement
    else if (isThinking && enableChatbot) {
      if (!smoothMorphTarget) {
        nodes.Wolf3D_Head.morphTargetInfluences[
          nodes.Wolf3D_Head.morphTargetDictionary["viseme_I"]
        ] = 0.3;
        nodes.Wolf3D_Teeth.morphTargetInfluences[
          nodes.Wolf3D_Teeth.morphTargetDictionary["viseme_I"]
        ] = 0.3;
      } else {
        nodes.Wolf3D_Head.morphTargetInfluences[
          nodes.Wolf3D_Head.morphTargetDictionary["viseme_I"]
        ] = THREE.MathUtils.lerp(
          nodes.Wolf3D_Head.morphTargetInfluences[
            nodes.Wolf3D_Head.morphTargetDictionary["viseme_I"]
          ],
          0.3,
          morphTargetSmoothing
        );
        nodes.Wolf3D_Teeth.morphTargetInfluences[
          nodes.Wolf3D_Teeth.morphTargetDictionary["viseme_I"]
        ] = THREE.MathUtils.lerp(
          nodes.Wolf3D_Teeth.morphTargetInfluences[
            nodes.Wolf3D_Teeth.morphTargetDictionary["viseme_I"]
          ],
          0.3,
          morphTargetSmoothing
        );
      }
    }
  });

  useEffect(() => {
    nodes.Wolf3D_Head.morphTargetInfluences[
      nodes.Wolf3D_Head.morphTargetDictionary["viseme_I"]
    ] = 1;
    nodes.Wolf3D_Teeth.morphTargetInfluences[
      nodes.Wolf3D_Teeth.morphTargetDictionary["viseme_I"]
    ] = 1;
    if (playAudio) {
      audio.play();
      if (script === "welcome") {
        setAnimation("Greeting");
      } else {
        setAnimation("Angry");
      }
    } else {
      setAnimation("Idle");
      audio.pause();
    }
  }, [playAudio, script]);

  const { nodes, materials } = useGLTF("/models/646d9dcdc8a5f5bddbfac913.glb");

  // Custom avatar texture handling
  const [customTexture, setCustomTexture] = useState(null);
  const [customMaterial, setCustomMaterial] = useState(null);

  useEffect(() => {
    if (customAvatar?.type === 'custom' && customAvatar?.data?.avatar) {
      // Load custom texture from the processed image
      const loader = new THREE.TextureLoader();
      loader.load(
        customAvatar.data.avatar,
        (texture) => {
          // Configure texture for better mapping
          texture.flipY = false;
          texture.wrapS = THREE.ClampToEdgeWrapping;
          texture.wrapT = THREE.ClampToEdgeWrapping;
          texture.minFilter = THREE.LinearFilter;
          texture.magFilter = THREE.LinearFilter;
          setCustomTexture(texture);

          // Create a simple material that replaces the head texture
          // but with better blending properties
          const material = new THREE.MeshStandardMaterial({
            map: texture,
            transparent: true,
            opacity: 0.95,
            roughness: 0.7,
            metalness: 0.1,
            // Use the same normal and other maps from original material if available
            normalMap: materials.Wolf3D_Skin.normalMap,
            roughnessMap: materials.Wolf3D_Skin.roughnessMap,
            metalnessMap: materials.Wolf3D_Skin.metalnessMap,
          });

          setCustomMaterial(material);
        },
        undefined,
        (error) => {
          console.error('Error loading custom avatar texture:', error);
        }
      );
    } else {
      setCustomTexture(null);
      setCustomMaterial(null);
    }
  }, [customAvatar, materials]);

  // Load animations with error handling
  const loadFBX = (path, animName) => {
    try {
      const { animations } = useFBX(path);
      if (animations && animations[0]) {
        animations[0].name = animName;
        return animations[0];
      }
      console.warn(`Failed to load animation: ${path}`);
      return null;
    } catch (error) {
      console.error(`Error loading animation ${path}:`, error);
      return null;
    }
  };

  // Load animations
  const idleAnimation = loadFBX("/animations/Idle.fbx", "Idle");
  const angryAnimation = loadFBX("/animations/Angry Gesture.fbx", "Angry");
  const greetingAnimation = loadFBX("/animations/Standing Greeting.fbx", "Greeting");

  // Filter out any null animations
  const validAnimations = [idleAnimation, angryAnimation, greetingAnimation].filter(Boolean);

  const [animation, setAnimation] = useState("Idle");
  const group = useRef();

  // Only create animations if we have valid ones
  const { actions } = useAnimations(
    validAnimations.length > 0 ? validAnimations : [],
    group
  );

  useEffect(() => {
    // Check if the animation exists before playing it
    if (actions && actions[animation]) {
      try {
        actions[animation].reset().fadeIn(0.5).play();
        return () => {
          if (actions[animation]) {
            actions[animation].fadeOut(0.5);
          }
        };
      } catch (error) {
        console.warn(`Error playing animation "${animation}":`, error.message);
      }
    } else {
      console.warn(`Animation "${animation}" not found in actions:`, Object.keys(actions || {}));
    }
  }, [animation, actions]);

  // Head tracking with error handling
  useFrame((state) => {
    if (headFollow && group.current) {
      const head = group.current.getObjectByName("Head");
      if (head) {
        head.lookAt(state.camera.position);
      }
    }
  });

  // Expose chatbot handler functions through a ref
  React.useImperativeHandle(props.avatarRef, () => ({
    handleNewMessage,
    handleThinking,
    handleStopThinking,
    isTalking,
    isThinking
  }), [isTalking, isThinking]);

  return (
    <group {...props} dispose={null} ref={group}>
      <primitive object={nodes.Hips} />
      <skinnedMesh
        geometry={nodes.Wolf3D_Body.geometry}
        material={materials.Wolf3D_Body}
        skeleton={nodes.Wolf3D_Body.skeleton}
      />
      <skinnedMesh
        geometry={nodes.Wolf3D_Outfit_Bottom.geometry}
        material={materials.Wolf3D_Outfit_Bottom}
        skeleton={nodes.Wolf3D_Outfit_Bottom.skeleton}
      />
      <skinnedMesh
        geometry={nodes.Wolf3D_Outfit_Footwear.geometry}
        material={materials.Wolf3D_Outfit_Footwear}
        skeleton={nodes.Wolf3D_Outfit_Footwear.skeleton}
      />
      <skinnedMesh
        geometry={nodes.Wolf3D_Outfit_Top.geometry}
        material={materials.Wolf3D_Outfit_Top}
        skeleton={nodes.Wolf3D_Outfit_Top.skeleton}
      />
      <skinnedMesh
        geometry={nodes.Wolf3D_Hair.geometry}
        material={materials.Wolf3D_Hair}
        skeleton={nodes.Wolf3D_Hair.skeleton}
      />
      <skinnedMesh
        name="EyeLeft"
        geometry={nodes.EyeLeft.geometry}
        material={materials.Wolf3D_Eye}
        skeleton={nodes.EyeLeft.skeleton}
        morphTargetDictionary={nodes.EyeLeft.morphTargetDictionary}
        morphTargetInfluences={nodes.EyeLeft.morphTargetInfluences}
      />
      <skinnedMesh
        name="EyeRight"
        geometry={nodes.EyeRight.geometry}
        material={materials.Wolf3D_Eye}
        skeleton={nodes.EyeRight.skeleton}
        morphTargetDictionary={nodes.EyeRight.morphTargetDictionary}
        morphTargetInfluences={nodes.EyeRight.morphTargetInfluences}
      />
      <skinnedMesh
        name="Wolf3D_Head"
        geometry={nodes.Wolf3D_Head.geometry}
        material={materials.Wolf3D_Skin}
        skeleton={nodes.Wolf3D_Head.skeleton}
        morphTargetDictionary={nodes.Wolf3D_Head.morphTargetDictionary}
        morphTargetInfluences={nodes.Wolf3D_Head.morphTargetInfluences}
      />
      {/* Face overlay for custom avatar */}
      {customTexture && !use3DReconstructedFace && (
        <mesh position={[0, 1.75, 0.12]} rotation={[0, 0, 0]}>
          <planeGeometry args={[0.28, 0.32]} />
          <meshBasicMaterial
            map={customTexture}
            transparent={true}
            opacity={0.95}
            alphaTest={0.1}
            side={THREE.DoubleSide}
          />
        </mesh>
      )}

      {/* 3D Reconstructed Face */}
      {reconstructed3DFace && use3DReconstructedFace && (
        <primitive
          object={reconstructed3DFace}
          position={[0, facePositionY, facePositionZ]}
          scale={[
            faceBlendStrength * faceScale,
            faceBlendStrength * faceScale,
            faceBlendStrength * faceScale
          ]}
          rotation={[0, 0, 0]}
        />
      )}

      <skinnedMesh
        name="Wolf3D_Teeth"
        geometry={nodes.Wolf3D_Teeth.geometry}
        material={materials.Wolf3D_Teeth}
        skeleton={nodes.Wolf3D_Teeth.skeleton}
        morphTargetDictionary={nodes.Wolf3D_Teeth.morphTargetDictionary}
        morphTargetInfluences={nodes.Wolf3D_Teeth.morphTargetInfluences}
      />
    </group>
  );
}

useGLTF.preload("/models/646d9dcdc8a5f5bddbfac913.glb");
