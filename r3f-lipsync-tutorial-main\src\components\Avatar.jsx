/*
Auto-generated by: https://github.com/pmndrs/gltfjsx
Command: npx gltfjsx@6.2.3 public/models/646d9dcdc8a5f5bddbfac913.glb -o src/components/Avatar.jsx -r public
*/

import { useAnimations, useFBX, useGLTF, useTexture } from "@react-three/drei";
import { useFrame, useLoader } from "@react-three/fiber";
import { useControls } from "leva";
import React, { useEffect, useMemo, useRef, useState } from "react";
import * as THREE from "three";
import CONFIG from "../config";
import { face3DReconstructor } from '../services/face3DReconstructor';
import { expressionMapper } from '../services/expressionMapper';
import { eyeTracker } from '../services/eyeTracker';
import { voiceAnalyzer } from '../services/voiceAnalyzer';

const corresponding = {
  A: "viseme_PP",
  B: "viseme_kk",
  C: "viseme_I",
  D: "viseme_AA",
  E: "viseme_O",
  F: "viseme_U",
  G: "viseme_FF",
  H: "viseme_TH",
  X: "viseme_PP",
};

export function Avatar(props) {
  const { customAvatar, reconstructed3DFace } = props;

  const {
    playAudio,
    script,
    headFollow,
    smoothMorphTarget,
    morphTargetSmoothing,
    enableChatbot,
    straightHandsPosition,
    use3DReconstructedFace,
    faceBlendStrength,
    facePositionY,
    facePositionZ,
    faceScale,
    enableExpressions,
    enableEyeTracking,
    enableVoiceAnalysis,
    expressionIntensity,
  } = useControls({
    playAudio: false,
    headFollow: true,
    smoothMorphTarget: true,
    morphTargetSmoothing: 0.5,
    enableChatbot: true,
    straightHandsPosition: true, // Control for keeping hands in a straight position
    use3DReconstructedFace: false, // Toggle for using 3D reconstructed face
    faceBlendStrength: { value: 1.0, min: 0, max: 1, step: 0.1 }, // Blend strength for 3D face
    facePositionY: { value: 1.75, min: 1.0, max: 2.5, step: 0.05 }, // Face Y position
    facePositionZ: { value: 0.15, min: -0.5, max: 0.5, step: 0.01 }, // Face Z position
    faceScale: { value: 0.35, min: 0.1, max: 1.0, step: 0.05 }, // Face scale
    enableExpressions: true, // Enable facial expressions
    enableEyeTracking: true, // Enable eye tracking and blinking
    enableVoiceAnalysis: false, // Enable real-time voice analysis
    expressionIntensity: { value: 0.7, min: 0, max: 1, step: 0.1 }, // Expression intensity
    script: {
      value: "welcome",
      options: ["welcome", "pizzas"],
    },
  });

  // State for chatbot integration
  const [isTalking, setIsTalking] = useState(false);
  const [currentMessage, setCurrentMessage] = useState("");
  const [isThinking, setIsThinking] = useState(false);
  const [chatbotLipsync, setChatbotLipsync] = useState({ mouthCues: [] });
  const [speechStartTime, setSpeechStartTime] = useState(0);

  // State for enhanced features
  const [currentExpression, setCurrentExpression] = useState('neutral');
  const [isVoiceAnalysisActive, setIsVoiceAnalysisActive] = useState(false);
  const [realtimeViseme, setRealtimeViseme] = useState('viseme_PP');
  const [realtimeIntensity, setRealtimeIntensity] = useState(0);

  // Create a speech synthesis instance
  const speechSynthesis = useMemo(() => window.speechSynthesis, []);

  // Audio for predefined scripts
  const audio = useMemo(() => new Audio(`/audios/${script}.mp3`), [script]);
  const jsonFile = useLoader(THREE.FileLoader, `audios/${script}.json`);
  const lipsync = JSON.parse(jsonFile);

  // Initialize enhanced systems
  useEffect(() => {
    // Initialize expression mapper
    if (enableExpressions) {
      expressionMapper.setExpression('neutral', expressionIntensity);
    }

    // Initialize voice analysis
    if (enableVoiceAnalysis && !isVoiceAnalysisActive) {
      initializeVoiceAnalysis();
    } else if (!enableVoiceAnalysis && isVoiceAnalysisActive) {
      voiceAnalyzer.stopAnalysis();
      setIsVoiceAnalysisActive(false);
    }
  }, [enableExpressions, enableVoiceAnalysis, expressionIntensity]);

  // Initialize voice analysis
  const initializeVoiceAnalysis = async () => {
    try {
      const initialized = await voiceAnalyzer.initialize();
      if (initialized) {
        // Set up callbacks
        voiceAnalyzer.setVisemeCallback((viseme, intensity) => {
          setRealtimeViseme(viseme);
          setRealtimeIntensity(intensity);
        });

        voiceAnalyzer.setSpeakingStateCallback((speaking) => {
          eyeTracker.setGazePattern(speaking ? 'speaking' : 'idle');
        });

        voiceAnalyzer.startAnalysis();
        setIsVoiceAnalysisActive(true);
        console.log('Voice analysis initialized and started');
      }
    } catch (error) {
      console.error('Failed to initialize voice analysis:', error);
    }
  };

  // Function to handle new messages from the chatbot
  const handleNewMessage = (message) => {
    setCurrentMessage(message);

    // Analyze sentiment for expressions
    if (enableExpressions) {
      const emotion = expressionMapper.analyzeSentiment(message);
      setCurrentExpression(emotion);
      expressionMapper.setExpression(emotion, expressionIntensity);
    }

    if (CONFIG.VOICE_ENABLED) {
      speakResponse(message);
    }

    // Check if message starts with "Hey there" for greeting animation
    if (message.startsWith("Hey there")) {
      setAnimation("Greeting");
      return;
    }

    // Determine emotion based on message content
    const emotion = analyzeSentiment(message);
    if (emotion === "happy" || emotion === "excited") {
      setAnimation("Greeting");
    } else if (emotion === "sad" || emotion === "angry") {
      setAnimation("Angry");
    } else {
      setAnimation("Idle");
    }
  };

  // Function to generate lipsync data for text
  const generateLipsyncData = (text) => {
    // Estimate speech duration based on text length and speaking rate
    // Average speaking rate is about 150 words per minute, or 2.5 words per second
    // Average word length is about 5 characters
    const estimatedDuration = (text.length / 5) / 2.5;

    // Create a lipsync data structure similar to the JSON files
    const lipsyncData = {
      metadata: {
        duration: estimatedDuration
      },
      mouthCues: []
    };

    // Start with mouth closed
    lipsyncData.mouthCues.push({ start: 0, end: 0.04, value: "X" });

    // Split text into words
    const words = text.split(/\s+/);
    let currentTime = 0.04;

    words.forEach((word, wordIndex) => {
      // Average time per word
      const wordDuration = word.length * 0.07;

      // Process each character in the word
      for (let i = 0; i < word.length; i++) {
        const char = word[i].toLowerCase();
        const charDuration = 0.07; // Average duration per character
        let visemeValue = "B"; // Default viseme

        // Map characters to visemes (using the same mapping as in the JSON files)
        if ("aeiou".includes(char)) {
          // Vowels
          if ("ae".includes(char)) visemeValue = "D";
          else if ("i".includes(char)) visemeValue = "C";
          else if ("o".includes(char)) visemeValue = "E";
          else if ("u".includes(char)) visemeValue = "F";
        } else if ("bmp".includes(char)) {
          // Bilabial consonants
          visemeValue = "A";
        } else if ("fv".includes(char)) {
          // Labiodental consonants
          visemeValue = "G";
        } else if ("th".includes(char)) {
          // Dental consonants
          visemeValue = "H";
        } else {
          // Other consonants
          visemeValue = "B";
        }

        // Add mouth cue
        lipsyncData.mouthCues.push({
          start: currentTime,
          end: currentTime + charDuration,
          value: visemeValue
        });

        currentTime += charDuration;
      }

      // Add a pause between words (except for the last word)
      if (wordIndex < words.length - 1) {
        lipsyncData.mouthCues.push({
          start: currentTime,
          end: currentTime + 0.1,
          value: "X"
        });
        currentTime += 0.1;
      }
    });

    // End with mouth closed
    lipsyncData.mouthCues.push({
      start: currentTime,
      end: currentTime + 0.1,
      value: "X"
    });

    // Update the total duration
    lipsyncData.metadata.duration = currentTime + 0.1;

    return lipsyncData;
  };

  // Function to speak the response
  const speakResponse = (text) => {
    if (speechSynthesis.speaking) {
      speechSynthesis.cancel();
    }

    // Generate lipsync data for the text
    const lipsyncData = generateLipsyncData(text);
    setChatbotLipsync(lipsyncData);

    const utterance = new SpeechSynthesisUtterance(text);
    utterance.lang = 'en-US';
    utterance.rate = 1.0;
    utterance.pitch = 1.0;

    setIsTalking(true);

    // Handle speech events
    utterance.onstart = () => {
      setSpeechStartTime(Date.now());
    };

    utterance.onend = () => {
      setIsTalking(false);
      setAnimation("Idle");
    };

    speechSynthesis.speak(utterance);
  };

  // Function to analyze sentiment of text
  const analyzeSentiment = (text) => {
    const positive = /great|happy|excellent|glad|good|wonderful|fantastic/i;
    const negative = /sorry|sad|unfortunate|bad|wrong|error/i;
    const thinking = /think|consider|perhaps|maybe|might|could/i;
    const excited = /excited|amazing|awesome|wow|incredible|love|best/i;
    const surprised = /surprised|unexpected|whoa|really|oh my|no way/i;

    if (excited.test(text)) return "excited";
    if (surprised.test(text)) return "surprised";
    if (positive.test(text)) return "happy";
    if (negative.test(text)) return "sad";
    if (thinking.test(text)) return "thinking";
    return "neutral";
  };

  // Functions to handle thinking state
  const handleThinking = () => {
    setIsThinking(true);
    setAnimation("Idle");
  };

  const handleStopThinking = () => {
    setIsThinking(false);
  };

  useFrame((state, deltaTime) => {
    // Update enhanced systems
    if (enableEyeTracking) {
      eyeTracker.update(deltaTime, isTalking, isThinking);
    }

    if (enableExpressions && reconstructed3DFace && use3DReconstructedFace) {
      expressionMapper.applyExpressionToMesh(reconstructed3DFace, deltaTime);
    }

    // Reset all morph targets for default avatar
    Object.values(corresponding).forEach((value) => {
      if (!smoothMorphTarget) {
        nodes.Wolf3D_Head.morphTargetInfluences[
          nodes.Wolf3D_Head.morphTargetDictionary[value]
        ] = 0;
        nodes.Wolf3D_Teeth.morphTargetInfluences[
          nodes.Wolf3D_Teeth.morphTargetDictionary[value]
        ] = 0;
      } else {
        nodes.Wolf3D_Head.morphTargetInfluences[
          nodes.Wolf3D_Head.morphTargetDictionary[value]
        ] = THREE.MathUtils.lerp(
          nodes.Wolf3D_Head.morphTargetInfluences[
            nodes.Wolf3D_Head.morphTargetDictionary[value]
          ],
          0,
          morphTargetSmoothing
        );

        nodes.Wolf3D_Teeth.morphTargetInfluences[
          nodes.Wolf3D_Teeth.morphTargetDictionary[value]
        ] = THREE.MathUtils.lerp(
          nodes.Wolf3D_Teeth.morphTargetInfluences[
            nodes.Wolf3D_Teeth.morphTargetDictionary[value]
          ],
          0,
          morphTargetSmoothing
        );
      }
    });

    // Reset custom face morphTargets if using 3D reconstructed face
    if (reconstructed3DFace && use3DReconstructedFace && reconstructed3DFace.morphTargetInfluences) {
      // Don't reset if we're applying expressions
      if (!enableExpressions) {
        reconstructed3DFace.morphTargetInfluences.fill(0);
      }
    }

    // Apply eye tracking to custom face
    if (enableEyeTracking && reconstructed3DFace && use3DReconstructedFace) {
      eyeTracker.applyToMesh(reconstructed3DFace);
    }

    // Adjust hand position if straightHandsPosition is enabled
    if (straightHandsPosition) {
      // Adjust arms to be straight at the sides
      // The exact bone names depend on the model's skeleton structure

      // Common bone naming patterns to try
      const leftArmBones = [
        'mixamorigLeftArm', 'LeftArm', 'Left_arm', 'left_arm',
        'mixamorigLeftShoulder', 'LeftShoulder'
      ];

      const rightArmBones = [
        'mixamorigRightArm', 'RightArm', 'Right_arm', 'right_arm',
        'mixamorigRightShoulder', 'RightShoulder'
      ];

      const leftForearmBones = [
        'mixamorigLeftForeArm', 'LeftForeArm', 'Left_forearm', 'left_forearm'
      ];

      const rightForearmBones = [
        'mixamorigRightForeArm', 'RightForeArm', 'Right_forearm', 'right_forearm'
      ];

      // Try to find and adjust left arm
      for (const boneName of leftArmBones) {
        if (nodes[boneName]) {
          nodes[boneName].rotation.z = THREE.MathUtils.lerp(
            nodes[boneName].rotation.z,
            0.1, // Slightly angled for natural position
            0.1  // Smoothing factor
          );
          nodes[boneName].rotation.x = THREE.MathUtils.lerp(
            nodes[boneName].rotation.x,
            0,   // Straight position
            0.1
          );
          break;
        }
      }

      // Try to find and adjust right arm
      for (const boneName of rightArmBones) {
        if (nodes[boneName]) {
          nodes[boneName].rotation.z = THREE.MathUtils.lerp(
            nodes[boneName].rotation.z,
            -0.1, // Slightly angled for natural position (mirrored from left)
            0.1
          );
          nodes[boneName].rotation.x = THREE.MathUtils.lerp(
            nodes[boneName].rotation.x,
            0,    // Straight position
            0.1
          );
          break;
        }
      }

      // Try to find and adjust left forearm
      for (const boneName of leftForearmBones) {
        if (nodes[boneName]) {
          nodes[boneName].rotation.z = THREE.MathUtils.lerp(
            nodes[boneName].rotation.z,
            0,    // Straight position
            0.1
          );
          break;
        }
      }

      // Try to find and adjust right forearm
      for (const boneName of rightForearmBones) {
        if (nodes[boneName]) {
          nodes[boneName].rotation.z = THREE.MathUtils.lerp(
            nodes[boneName].rotation.z,
            0,    // Straight position
            0.1
          );
          break;
        }
      }
    }

    // Handle real-time voice analysis
    if (enableVoiceAnalysis && isVoiceAnalysisActive && realtimeIntensity > 0) {
      const visemeShape = realtimeViseme;

      // Apply to default avatar if not using custom face
      if (!use3DReconstructedFace) {
        if (!smoothMorphTarget) {
          nodes.Wolf3D_Head.morphTargetInfluences[
            nodes.Wolf3D_Head.morphTargetDictionary[visemeShape]
          ] = realtimeIntensity;
          nodes.Wolf3D_Teeth.morphTargetInfluences[
            nodes.Wolf3D_Teeth.morphTargetDictionary[visemeShape]
          ] = realtimeIntensity;
        } else {
          nodes.Wolf3D_Head.morphTargetInfluences[
            nodes.Wolf3D_Head.morphTargetDictionary[visemeShape]
          ] = THREE.MathUtils.lerp(
            nodes.Wolf3D_Head.morphTargetInfluences[
              nodes.Wolf3D_Head.morphTargetDictionary[visemeShape]
            ],
            realtimeIntensity,
            morphTargetSmoothing
          );
          nodes.Wolf3D_Teeth.morphTargetInfluences[
            nodes.Wolf3D_Teeth.morphTargetDictionary[visemeShape]
          ] = THREE.MathUtils.lerp(
            nodes.Wolf3D_Teeth.morphTargetInfluences[
              nodes.Wolf3D_Teeth.morphTargetDictionary[visemeShape]
            ],
            realtimeIntensity,
            morphTargetSmoothing
          );
        }
      }

      // Apply to custom 3D face if available
      if (reconstructed3DFace && use3DReconstructedFace && reconstructed3DFace.morphTargetDictionary) {
        const customIndex = reconstructed3DFace.morphTargetDictionary[visemeShape];
        if (customIndex !== undefined && reconstructed3DFace.morphTargetInfluences) {
          if (!smoothMorphTarget) {
            reconstructed3DFace.morphTargetInfluences[customIndex] = realtimeIntensity;
          } else {
            reconstructed3DFace.morphTargetInfluences[customIndex] = THREE.MathUtils.lerp(
              reconstructed3DFace.morphTargetInfluences[customIndex],
              realtimeIntensity,
              morphTargetSmoothing
            );
          }
        }
      }
    }
    // Handle predefined audio playback
    else if (playAudio && !audio.paused && !audio.ended) {
      const currentAudioTime = audio.currentTime;

      for (let i = 0; i < lipsync.mouthCues.length; i++) {
        const mouthCue = lipsync.mouthCues[i];
        if (
          currentAudioTime >= mouthCue.start &&
          currentAudioTime <= mouthCue.end
        ) {
          const visemeShape = corresponding[mouthCue.value];

          // Apply to default avatar if not using custom face
          if (!use3DReconstructedFace) {
            if (!smoothMorphTarget) {
              nodes.Wolf3D_Head.morphTargetInfluences[
                nodes.Wolf3D_Head.morphTargetDictionary[visemeShape]
              ] = 1;
              nodes.Wolf3D_Teeth.morphTargetInfluences[
                nodes.Wolf3D_Teeth.morphTargetDictionary[visemeShape]
              ] = 1;
            } else {
              nodes.Wolf3D_Head.morphTargetInfluences[
                nodes.Wolf3D_Head.morphTargetDictionary[visemeShape]
              ] = THREE.MathUtils.lerp(
                nodes.Wolf3D_Head.morphTargetInfluences[
                  nodes.Wolf3D_Head.morphTargetDictionary[visemeShape]
                ],
                1,
                morphTargetSmoothing
              );
              nodes.Wolf3D_Teeth.morphTargetInfluences[
                nodes.Wolf3D_Teeth.morphTargetDictionary[visemeShape]
              ] = THREE.MathUtils.lerp(
                nodes.Wolf3D_Teeth.morphTargetInfluences[
                  nodes.Wolf3D_Teeth.morphTargetDictionary[visemeShape]
                ],
                1,
                morphTargetSmoothing
              );
            }
          }

          // Apply to custom 3D face if available
          if (reconstructed3DFace && use3DReconstructedFace && reconstructed3DFace.morphTargetDictionary) {
            const customIndex = reconstructed3DFace.morphTargetDictionary[visemeShape];
            if (customIndex !== undefined && reconstructed3DFace.morphTargetInfluences) {
              if (!smoothMorphTarget) {
                reconstructed3DFace.morphTargetInfluences[customIndex] = 1;
              } else {
                reconstructed3DFace.morphTargetInfluences[customIndex] = THREE.MathUtils.lerp(
                  reconstructed3DFace.morphTargetInfluences[customIndex],
                  1,
                  morphTargetSmoothing
                );
              }
            }
          }
          break;
        }
      }
    }
    // Handle chatbot talking
    else if (isTalking && enableChatbot) {
      // Use the same approach as the predefined audio playback
      const currentTime = (Date.now() - speechStartTime) / 1000;

      // Find the current mouth cue based on the elapsed time
      let currentMouthCue = null;

      for (let i = 0; i < chatbotLipsync.mouthCues.length; i++) {
        const mouthCue = chatbotLipsync.mouthCues[i];
        if (
          currentTime >= mouthCue.start &&
          currentTime <= mouthCue.end
        ) {
          currentMouthCue = mouthCue;
          break;
        }
      }

      // If we found a mouth cue, apply it
      if (currentMouthCue) {
        const visemeShape = corresponding[currentMouthCue.value];

        // Apply to default avatar if not using custom face
        if (!use3DReconstructedFace) {
          if (!smoothMorphTarget) {
            nodes.Wolf3D_Head.morphTargetInfluences[
              nodes.Wolf3D_Head.morphTargetDictionary[visemeShape]
            ] = 1;
            nodes.Wolf3D_Teeth.morphTargetInfluences[
              nodes.Wolf3D_Teeth.morphTargetDictionary[visemeShape]
            ] = 1;
          } else {
            nodes.Wolf3D_Head.morphTargetInfluences[
              nodes.Wolf3D_Head.morphTargetDictionary[visemeShape]
            ] = THREE.MathUtils.lerp(
              nodes.Wolf3D_Head.morphTargetInfluences[
                nodes.Wolf3D_Head.morphTargetDictionary[visemeShape]
              ],
              1,
              morphTargetSmoothing
            );
            nodes.Wolf3D_Teeth.morphTargetInfluences[
              nodes.Wolf3D_Teeth.morphTargetDictionary[visemeShape]
            ] = THREE.MathUtils.lerp(
              nodes.Wolf3D_Teeth.morphTargetInfluences[
                nodes.Wolf3D_Teeth.morphTargetDictionary[visemeShape]
              ],
              1,
              morphTargetSmoothing
            );
          }
        }

        // Apply to custom 3D face if available
        if (reconstructed3DFace && use3DReconstructedFace && reconstructed3DFace.morphTargetDictionary) {
          const customIndex = reconstructed3DFace.morphTargetDictionary[visemeShape];
          if (customIndex !== undefined && reconstructed3DFace.morphTargetInfluences) {
            if (!smoothMorphTarget) {
              reconstructed3DFace.morphTargetInfluences[customIndex] = 1;
            } else {
              reconstructed3DFace.morphTargetInfluences[customIndex] = THREE.MathUtils.lerp(
                reconstructed3DFace.morphTargetInfluences[customIndex],
                1,
                morphTargetSmoothing
              );
            }
          }
        }
      } else {
        // If we're past the end of the lipsync data, use a closed mouth
        const visemeShape = corresponding["X"];

        if (!smoothMorphTarget) {
          nodes.Wolf3D_Head.morphTargetInfluences[
            nodes.Wolf3D_Head.morphTargetDictionary[visemeShape]
          ] = 1;
          nodes.Wolf3D_Teeth.morphTargetInfluences[
            nodes.Wolf3D_Teeth.morphTargetDictionary[visemeShape]
          ] = 1;
        } else {
          nodes.Wolf3D_Head.morphTargetInfluences[
            nodes.Wolf3D_Head.morphTargetDictionary[visemeShape]
          ] = THREE.MathUtils.lerp(
            nodes.Wolf3D_Head.morphTargetInfluences[
              nodes.Wolf3D_Head.morphTargetDictionary[visemeShape]
            ],
            1,
            morphTargetSmoothing
          );
          nodes.Wolf3D_Teeth.morphTargetInfluences[
            nodes.Wolf3D_Teeth.morphTargetDictionary[visemeShape]
          ] = THREE.MathUtils.lerp(
            nodes.Wolf3D_Teeth.morphTargetInfluences[
              nodes.Wolf3D_Teeth.morphTargetDictionary[visemeShape]
            ],
            1,
            morphTargetSmoothing
          );
        }
      }
    }
    // If thinking, show a slight mouth movement
    else if (isThinking && enableChatbot) {
      if (!smoothMorphTarget) {
        nodes.Wolf3D_Head.morphTargetInfluences[
          nodes.Wolf3D_Head.morphTargetDictionary["viseme_I"]
        ] = 0.3;
        nodes.Wolf3D_Teeth.morphTargetInfluences[
          nodes.Wolf3D_Teeth.morphTargetDictionary["viseme_I"]
        ] = 0.3;
      } else {
        nodes.Wolf3D_Head.morphTargetInfluences[
          nodes.Wolf3D_Head.morphTargetDictionary["viseme_I"]
        ] = THREE.MathUtils.lerp(
          nodes.Wolf3D_Head.morphTargetInfluences[
            nodes.Wolf3D_Head.morphTargetDictionary["viseme_I"]
          ],
          0.3,
          morphTargetSmoothing
        );
        nodes.Wolf3D_Teeth.morphTargetInfluences[
          nodes.Wolf3D_Teeth.morphTargetDictionary["viseme_I"]
        ] = THREE.MathUtils.lerp(
          nodes.Wolf3D_Teeth.morphTargetInfluences[
            nodes.Wolf3D_Teeth.morphTargetDictionary["viseme_I"]
          ],
          0.3,
          morphTargetSmoothing
        );
      }
    }
  });

  useEffect(() => {
    nodes.Wolf3D_Head.morphTargetInfluences[
      nodes.Wolf3D_Head.morphTargetDictionary["viseme_I"]
    ] = 1;
    nodes.Wolf3D_Teeth.morphTargetInfluences[
      nodes.Wolf3D_Teeth.morphTargetDictionary["viseme_I"]
    ] = 1;
    if (playAudio) {
      audio.play();
      if (script === "welcome") {
        setAnimation("Greeting");
      } else {
        setAnimation("Angry");
      }
    } else {
      setAnimation("Idle");
      audio.pause();
    }
  }, [playAudio, script]);

  const { nodes, materials } = useGLTF("/models/646d9dcdc8a5f5bddbfac913.glb");

  // Custom avatar texture handling
  const [customTexture, setCustomTexture] = useState(null);
  const [customMaterial, setCustomMaterial] = useState(null);

  useEffect(() => {
    if (customAvatar?.type === 'custom' && customAvatar?.data?.avatar) {
      // Load custom texture from the processed image
      const loader = new THREE.TextureLoader();
      loader.load(
        customAvatar.data.avatar,
        (texture) => {
          // Configure texture for better mapping
          texture.flipY = false;
          texture.wrapS = THREE.ClampToEdgeWrapping;
          texture.wrapT = THREE.ClampToEdgeWrapping;
          texture.minFilter = THREE.LinearFilter;
          texture.magFilter = THREE.LinearFilter;
          setCustomTexture(texture);

          // Create a simple material that replaces the head texture
          // but with better blending properties
          const material = new THREE.MeshStandardMaterial({
            map: texture,
            transparent: true,
            opacity: 0.95,
            roughness: 0.7,
            metalness: 0.1,
            // Use the same normal and other maps from original material if available
            normalMap: materials.Wolf3D_Skin.normalMap,
            roughnessMap: materials.Wolf3D_Skin.roughnessMap,
            metalnessMap: materials.Wolf3D_Skin.metalnessMap,
          });

          setCustomMaterial(material);
        },
        undefined,
        (error) => {
          console.error('Error loading custom avatar texture:', error);
        }
      );
    } else {
      setCustomTexture(null);
      setCustomMaterial(null);
    }
  }, [customAvatar, materials]);

  // Load animations with error handling
  const loadFBX = (path, animName) => {
    try {
      const { animations } = useFBX(path);
      if (animations && animations[0]) {
        animations[0].name = animName;
        return animations[0];
      }
      console.warn(`Failed to load animation: ${path}`);
      return null;
    } catch (error) {
      console.error(`Error loading animation ${path}:`, error);
      return null;
    }
  };

  // Load animations
  const idleAnimation = loadFBX("/animations/Idle.fbx", "Idle");
  const angryAnimation = loadFBX("/animations/Angry Gesture.fbx", "Angry");
  const greetingAnimation = loadFBX("/animations/Standing Greeting.fbx", "Greeting");

  // Filter out any null animations
  const validAnimations = [idleAnimation, angryAnimation, greetingAnimation].filter(Boolean);

  const [animation, setAnimation] = useState("Idle");
  const group = useRef();

  // Only create animations if we have valid ones
  const { actions } = useAnimations(
    validAnimations.length > 0 ? validAnimations : [],
    group
  );

  useEffect(() => {
    // Check if the animation exists before playing it
    if (actions && actions[animation]) {
      try {
        actions[animation].reset().fadeIn(0.5).play();
        return () => {
          if (actions[animation]) {
            actions[animation].fadeOut(0.5);
          }
        };
      } catch (error) {
        console.warn(`Error playing animation "${animation}":`, error.message);
      }
    } else {
      console.warn(`Animation "${animation}" not found in actions:`, Object.keys(actions || {}));
    }
  }, [animation, actions]);

  // Head tracking with error handling
  useFrame((state) => {
    if (headFollow && group.current) {
      const head = group.current.getObjectByName("Head");
      if (head) {
        head.lookAt(state.camera.position);
      }
    }
  });

  // Expose chatbot handler functions through a ref
  React.useImperativeHandle(props.avatarRef, () => ({
    handleNewMessage,
    handleThinking,
    handleStopThinking,
    isTalking,
    isThinking
  }), [isTalking, isThinking]);

  return (
    <group {...props} dispose={null} ref={group}>
      <primitive object={nodes.Hips} />
      <skinnedMesh
        geometry={nodes.Wolf3D_Body.geometry}
        material={materials.Wolf3D_Body}
        skeleton={nodes.Wolf3D_Body.skeleton}
      />
      <skinnedMesh
        geometry={nodes.Wolf3D_Outfit_Bottom.geometry}
        material={materials.Wolf3D_Outfit_Bottom}
        skeleton={nodes.Wolf3D_Outfit_Bottom.skeleton}
      />
      <skinnedMesh
        geometry={nodes.Wolf3D_Outfit_Footwear.geometry}
        material={materials.Wolf3D_Outfit_Footwear}
        skeleton={nodes.Wolf3D_Outfit_Footwear.skeleton}
      />
      <skinnedMesh
        geometry={nodes.Wolf3D_Outfit_Top.geometry}
        material={materials.Wolf3D_Outfit_Top}
        skeleton={nodes.Wolf3D_Outfit_Top.skeleton}
      />
      <skinnedMesh
        geometry={nodes.Wolf3D_Hair.geometry}
        material={materials.Wolf3D_Hair}
        skeleton={nodes.Wolf3D_Hair.skeleton}
      />
      <skinnedMesh
        name="EyeLeft"
        geometry={nodes.EyeLeft.geometry}
        material={materials.Wolf3D_Eye}
        skeleton={nodes.EyeLeft.skeleton}
        morphTargetDictionary={nodes.EyeLeft.morphTargetDictionary}
        morphTargetInfluences={nodes.EyeLeft.morphTargetInfluences}
      />
      <skinnedMesh
        name="EyeRight"
        geometry={nodes.EyeRight.geometry}
        material={materials.Wolf3D_Eye}
        skeleton={nodes.EyeRight.skeleton}
        morphTargetDictionary={nodes.EyeRight.morphTargetDictionary}
        morphTargetInfluences={nodes.EyeRight.morphTargetInfluences}
      />
      <skinnedMesh
        name="Wolf3D_Head"
        geometry={nodes.Wolf3D_Head.geometry}
        material={materials.Wolf3D_Skin}
        skeleton={nodes.Wolf3D_Head.skeleton}
        morphTargetDictionary={nodes.Wolf3D_Head.morphTargetDictionary}
        morphTargetInfluences={nodes.Wolf3D_Head.morphTargetInfluences}
      />
      {/* Face overlay for custom avatar */}
      {customTexture && !use3DReconstructedFace && (
        <mesh position={[0, 1.75, 0.12]} rotation={[0, 0, 0]}>
          <planeGeometry args={[0.28, 0.32]} />
          <meshBasicMaterial
            map={customTexture}
            transparent={true}
            opacity={0.95}
            alphaTest={0.1}
            side={THREE.DoubleSide}
          />
        </mesh>
      )}

      {/* 3D Reconstructed Face */}
      {reconstructed3DFace && use3DReconstructedFace && (
        <primitive
          object={reconstructed3DFace}
          position={[0, facePositionY, facePositionZ]}
          scale={[
            faceBlendStrength * faceScale,
            faceBlendStrength * faceScale,
            faceBlendStrength * faceScale
          ]}
          rotation={[0, 0, 0]}
        />
      )}

      <skinnedMesh
        name="Wolf3D_Teeth"
        geometry={nodes.Wolf3D_Teeth.geometry}
        material={materials.Wolf3D_Teeth}
        skeleton={nodes.Wolf3D_Teeth.skeleton}
        morphTargetDictionary={nodes.Wolf3D_Teeth.morphTargetDictionary}
        morphTargetInfluences={nodes.Wolf3D_Teeth.morphTargetInfluences}
      />
    </group>
  );
}

useGLTF.preload("/models/646d9dcdc8a5f5bddbfac913.glb");
