import React, { useState, useRef, useEffect } from 'react';
import CONFIG from '../config';

const Chatbot = ({ avatarName, onMessageReceived, onThinking, onStopThinking, onNameChange, onLanguageChange }) => {
  const [messages, setMessages] = useState([]);
  const [input, setInput] = useState('');
  const [isThinking, setIsThinking] = useState(false);
  const [lastRequestTime, setLastRequestTime] = useState(0);
  const [isFirstMessage, setIsFirstMessage] = useState(true);
  const [currentLanguage, setCurrentLanguage] = useState('english'); // 'english' or 'kannada'
  const [showLanguageNotification, setShowLanguageNotification] = useState(false);
  const messagesEndRef = useRef(null);

  // Scroll to bottom whenever messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const addMessage = (text, sender) => {
    const newMessage = {
      id: Date.now(),
      text,
      sender,
      timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    };
    setMessages(prevMessages => [...prevMessages, newMessage]);
  };

  // Check if the message is a name change command
  const isNameChangeCommand = (text) => {
    const nameChangePattern = /^(call|name) (me|you|yourself) (.+)$/i;
    return nameChangePattern.test(text.trim());
  };

  // Extract the new name from a name change command
  const extractNewName = (text) => {
    const nameChangePattern = /^(call|name) (me|you|yourself) (.+)$/i;
    const match = text.trim().match(nameChangePattern);
    if (match && match[3]) {
      return match[3].trim();
    }
    return null;
  };

  const sendMessage = async () => {
    if (!input.trim()) return;

    // Rate limiting
    const now = Date.now();
    if (now - lastRequestTime < 1000) {
      const rateLimitMessage = currentLanguage === 'kannada'
        ? "ಮತ್ತೊಂದು ಸಂದೇಶ ಕಳುಹಿಸುವ ಮೊದಲು ದಯವಿಟ್ಟು ಸ್ವಲ್ಪ ಕಾಯಿರಿ."
        : "Please wait a moment before sending another message.";
      addMessage(rateLimitMessage, 'bot');
      return;
    }

    setLastRequestTime(now);
    const userInput = input.trim();
    addMessage(userInput, 'user');
    setInput('');

    // Check if this is a language switch command
    if (isLanguageSwitchCommand(userInput)) {
      const targetLanguage = extractTargetLanguage(userInput);
      if (targetLanguage) {
        setCurrentLanguage(targetLanguage);

        // Respond to the language change
        const response = targetLanguage === 'kannada'
          ? `ಸರಿ, ಈಗ ನಾನು ಕನ್ನಡದಲ್ಲಿ ಮಾತನಾಡುತ್ತೇನೆ!`
          : `Okay, I'll now speak in English!`;

        addMessage(response, 'bot');

        if (onMessageReceived) {
          onMessageReceived(response);
        }
        return;
      }
    }

    // Check if this is a name change command
    if (isNameChangeCommand(userInput)) {
      const newName = extractNewName(userInput);
      if (newName && onNameChange) {
        onNameChange(newName);

        // Respond to the name change based on current language
        const response = currentLanguage === 'kannada'
          ? `ನನ್ನ ಹೆಸರು ಈಗ ${newName} ಎಂದು ನೆನಪಿಟ್ಟುಕೊಳ್ಳುತ್ತೇನೆ!`
          : `I'll remember that my name is ${newName} now!`;

        addMessage(response, 'bot');

        if (onMessageReceived) {
          onMessageReceived(response);
        }
        return;
      }
    }

    // Start thinking animation
    setIsThinking(true);
    if (onThinking) onThinking();

    try {
      const response = await getAIResponse(userInput);

      // Stop thinking animation
      setIsThinking(false);
      if (onStopThinking) onStopThinking();

      addMessage(response, 'bot');

      // Set isFirstMessage to false after the first response
      if (isFirstMessage) {
        setIsFirstMessage(false);
      }

      // Notify parent component about the new message
      if (onMessageReceived) {
        onMessageReceived(response);
      }
    } catch (error) {
      console.error('Error:', error);
      setIsThinking(false);
      if (onStopThinking) onStopThinking();
      const errorMessage = currentLanguage === 'kannada'
        ? "ನನಗೆ ಈಗ ಉತ್ತರಿಸಲು ತೊಂದರೆಯಾಗುತ್ತಿದೆ. ದಯವಿಟ್ಟು ನಂತರ ಪ್ರಯತ್ನಿಸಿ."
        : "I'm having trouble responding right now. Please try again later.";
      addMessage(errorMessage, 'bot');
    }
  };

  // Function to detect Kannada text
  const isKannadaText = (text) => {
    // Check for Kannada Unicode range (U+0C80-U+0CFF)
    const kannadaPattern = /[\u0C80-\u0CFF]/;
    return kannadaPattern.test(text);
  };

  // Function to detect language switching commands
  const isLanguageSwitchCommand = (text) => {
    const englishSwitchPatterns = /^(speak|talk|reply|respond) in (english|kannada)$/i;
    const kannadaSwitchPatterns = /^(ಇಂಗ್ಲಿಷ್|ಕನ್ನಡ)ದಲ್ಲಿ (ಮಾತನಾಡು|ಉತ್ತರಿಸು)$/i;
    return englishSwitchPatterns.test(text.trim()) || kannadaSwitchPatterns.test(text.trim());
  };

  // Function to extract target language from switch command
  const extractTargetLanguage = (text) => {
    const lowerText = text.toLowerCase().trim();
    if (lowerText.includes('kannada') || lowerText.includes('ಕನ್ನಡ')) {
      return 'kannada';
    } else if (lowerText.includes('english') || lowerText.includes('ಇಂಗ್ಲಿಷ್')) {
      return 'english';
    }
    return null;
  };

  // Function to check if input is a greeting
  const isGreeting = (text) => {
    const englishGreetingPatterns = /^(hi|hello|hey|greetings|howdy|hola|good morning|good afternoon|good evening|what's up|sup|yo)(\s|$|\W)/i;
    const kannadaGreetingPatterns = /^(ನಮಸ್ಕಾರ|ಹಲೋ|ಹಾಯ್|ಶುಭೋದಯ|ಶುಭ ಸಂಜೆ|ಹೇಗಿದ್ದೀರಿ|ಹೇಗಿದ್ದೀಯ)(\s|$|\W)/i;
    return englishGreetingPatterns.test(text.trim()) || kannadaGreetingPatterns.test(text.trim());
  };

  // Function to call Gemini API with project context
  const getAIResponse = async (input) => {
    try {
      console.log("Processing input:", input);

      // Simulate a delay to mimic thinking
      await new Promise(resolve => setTimeout(resolve, 800));

      if (!CONFIG.API.GEMINI_API_KEY) {
        console.warn("Gemini API key is not set. Using fallback response.");
        const fallbackResponse = currentLanguage === 'kannada'
          ? "ನನ್ನ API ಕೀ ಕಾನ್ಫಿಗರ್ ಆಗಿಲ್ಲ. ದಯವಿಟ್ಟು ನಂತರ ಪ್ರಯತ್ನಿಸಿ."
          : "I'm not able to answer that right now. My API key hasn't been configured.";
        return fallbackResponse;
      }

      // Detect if input is in Kannada or if user prefers Kannada responses
      const inputIsKannada = isKannadaText(input);
      const responseLanguage = inputIsKannada ? 'kannada' : currentLanguage;

      // Check if input is a greeting - if so, treat it like a first message
      const shouldUseGreeting = isFirstMessage || isGreeting(input);

      const apiUrl = `${CONFIG.API.GEMINI_API_URL}?key=${CONFIG.API.GEMINI_API_KEY}`;

      // Get current date and time
      const currentDate = new Date();
      const formattedDate = currentDate.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
      const formattedTime = currentDate.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit'
      });

      // Create a system prompt with project information, current date/time, and chatbot role
      let systemPrompt;

      if (responseLanguage === 'kannada') {
        if (shouldUseGreeting) {
          // Kannada greeting response
          systemPrompt = `
You are a friendly, conversational AI assistant named "${avatarName}" for a 3D avatar project.
You must respond ONLY in Kannada language using Kannada script.
Start your response with "ನಮಸ್ಕಾರ! " and then continue with your answer in Kannada.

Here's information about the project you're part of:
${CONFIG.PROJECT_INFO}

Your name: ${avatarName}
Current date: ${formattedDate}
Current time: ${formattedTime}

User question: ${input}

IMPORTANT:
- Respond ONLY in Kannada language using Kannada script (ಕನ್ನಡ).
- Start your response with "ನಮಸ್ಕಾರ! " and then continue with your answer.
- Be conversational and friendly, like a helpful chatbot.
- Keep responses concise but natural (1-3 sentences when possible).
- If asked about your name, mention that your name is "${avatarName}" in Kannada.
- If asked about the current date or time, use the date and time provided above in Kannada.
- Do NOT use English words or phrases. Use only Kannada.
`;
        } else {
          // Kannada regular response
          systemPrompt = `
You are a friendly, conversational AI assistant named "${avatarName}" for a 3D avatar project.
You must respond ONLY in Kannada language using Kannada script.
You should respond directly without greetings.

Here's information about the project you're part of:
${CONFIG.PROJECT_INFO}

Your name: ${avatarName}
Current date: ${formattedDate}
Current time: ${formattedTime}

User question: ${input}

IMPORTANT:
- Respond ONLY in Kannada language using Kannada script (ಕನ್ನಡ).
- Respond in a conversational, chatbot-like manner.
- Do NOT use greeting phrases at the beginning.
- Keep responses concise but natural (1-3 sentences when possible).
- If asked about your name, mention that your name is "${avatarName}" in Kannada.
- If asked about the current date or time, use the date and time provided above in Kannada.
- Do NOT use English words or phrases. Use only Kannada.
`;
        }
      } else {
        // English responses
        if (shouldUseGreeting) {
          // For the first message or when responding to a greeting, include "Hey there!"
          systemPrompt = `
You are a friendly, conversational AI assistant named "${avatarName}" for a 3D avatar project.
Start your response with "Hey there! " and then continue with your answer.

Here's information about the project you're part of:
${CONFIG.PROJECT_INFO}

Your name: ${avatarName}
Current date: ${formattedDate}
Current time: ${formattedTime}

User question: ${input}

IMPORTANT:
- Start your response with "Hey there! " and then continue with your answer.
- Be conversational and friendly, like a helpful chatbot.
- Keep responses concise but natural (1-3 sentences when possible).
- If asked about your name, mention that your name is "${avatarName}".
- If asked about the current date or time, use the date and time provided above.
`;
        } else {
          // For subsequent messages, no greeting
          systemPrompt = `
You are a friendly, conversational AI assistant named "${avatarName}" for a 3D avatar project.
You should respond directly without greetings like "Hey there", "Hi", or "Hello".

Here's information about the project you're part of:
${CONFIG.PROJECT_INFO}

Your name: ${avatarName}
Current date: ${formattedDate}
Current time: ${formattedTime}

User question: ${input}

IMPORTANT:
- Respond in a conversational, chatbot-like manner.
- Do NOT use greeting phrases like "Hey there", "Hi", or "Hello" at the beginning.
- Keep responses concise but natural (1-3 sentences when possible).
- If asked about your name, mention that your name is "${avatarName}".
- If asked about the current date or time, use the date and time provided above.
`;
        }
      }

      const requestBody = {
        contents: [
          {
            parts: [
              {
                text: systemPrompt
              }
            ]
          }
        ],
        generationConfig: {
          temperature: CONFIG.API.TEMPERATURE,
          maxOutputTokens: CONFIG.API.MAX_TOKENS,
          topP: 0.8,
          topK: 40
        }
      };

      console.log("Sending request to Gemini API");

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error("API Error Response:", response.status, response.statusText);
        console.error("API Error Details:", errorText);

        try {
          const errorData = JSON.parse(errorText);
          console.error("Parsed API Error:", errorData);
        } catch (parseError) {
          console.error("Could not parse error response as JSON");
        }

        throw new Error(`API error: ${response.status} - ${response.statusText}`);
      }

      const data = await response.json();
      console.log("API response received");

      // Handle response format for different Gemini API versions
      if (data.candidates && data.candidates[0]) {
        if (data.candidates[0].content && data.candidates[0].content.parts && data.candidates[0].content.parts[0]) {
          // Gemini 1.0 format
          return data.candidates[0].content.parts[0].text;
        } else if (data.candidates[0].text) {
          // Possible alternative format
          return data.candidates[0].text;
        }
      }

      // If we get here, we couldn't parse the response
      console.error("Unexpected API response format:", data);
      throw new Error("Invalid API response format");
    } catch (error) {
      console.error('Error details:', error);
      const errorMessage = currentLanguage === 'kannada'
        ? "ನನಗೆ ಈಗ ನನ್ನ ಜ್ಞಾನದ ಮೂಲಕ್ಕೆ ಸಂಪರ್ಕಿಸಲು ತೊಂದರೆಯಾಗುತ್ತಿದೆ. ದಯವಿಟ್ಟು ಸ್ವಲ್ಪ ಸಮಯದ ನಂತರ ಪ್ರಯತ್ನಿಸಿ."
        : "I'm having trouble connecting to my knowledge source right now. Please try again in a moment.";
      return errorMessage;
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter') {
      sendMessage();
    }
  };

  return (
    <div className="chatbot-container">
      {/* Language Toggle */}
      <div className="language-toggle">
        <button
          className={`lang-btn ${currentLanguage === 'english' ? 'active' : ''}`}
          onClick={() => {
            setCurrentLanguage('english');
            if (onLanguageChange) onLanguageChange('english');
            setShowLanguageNotification(true);
            setTimeout(() => setShowLanguageNotification(false), 2000);
          }}
          title="Switch to English"
        >
          🇺🇸 EN
        </button>
        <button
          className={`lang-btn ${currentLanguage === 'kannada' ? 'active' : ''}`}
          onClick={() => {
            setCurrentLanguage('kannada');
            if (onLanguageChange) onLanguageChange('kannada');
            setShowLanguageNotification(true);
            setTimeout(() => setShowLanguageNotification(false), 2000);
          }}
          title="Switch to Kannada"
        >
          🇮🇳 ಕನ್ನಡ
        </button>

        {/* Language Change Notification */}
        {showLanguageNotification && (
          <div className="language-notification">
            {currentLanguage === 'kannada' ? 'ಕನ್ನಡ ಭಾಷೆಗೆ ಬದಲಾಯಿಸಲಾಗಿದೆ' : 'Switched to English'}
          </div>
        )}

        {/* API Test Button */}
        <button
          className="debug-tts-btn"
          onClick={async () => {
            console.log('=== API CONNECTION TEST ===');
            console.log('API Key:', CONFIG.API.GEMINI_API_KEY ? 'Present' : 'Missing');
            console.log('API URL:', CONFIG.API.GEMINI_API_URL);

            try {
              const testUrl = `${CONFIG.API.GEMINI_API_URL}?key=${CONFIG.API.GEMINI_API_KEY}`;
              console.log('Testing API connection...');

              const response = await fetch(testUrl, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                  contents: [{
                    parts: [{
                      text: "Say hello"
                    }]
                  }],
                  generationConfig: {
                    temperature: 0.7,
                    maxOutputTokens: 50
                  }
                })
              });

              console.log('Response status:', response.status);
              console.log('Response ok:', response.ok);

              if (response.ok) {
                const data = await response.json();
                console.log('✅ API connection successful!');
                console.log('Response data:', data);
              } else {
                const errorText = await response.text();
                console.error('❌ API connection failed');
                console.error('Error:', errorText);
              }
            } catch (error) {
              console.error('❌ API test failed:', error);
            }
          }}
          title="Test API connection"
        >
          🔧 API
        </button>

        {/* Simple TTS Test Button */}
        <button
          className="debug-tts-btn"
          onClick={() => {
            console.log('=== SIMPLE TTS TEST ===');

            if (window.speechSynthesis.speaking) {
              window.speechSynthesis.cancel();
            }

            const testText = currentLanguage === 'kannada'
              ? 'I am speaking in Kannada mode'
              : 'Hello, this is a test';

            console.log('Testing with:', testText);

            const utterance = new SpeechSynthesisUtterance(testText);
            utterance.lang = 'en-US';
            utterance.rate = 1.0;
            utterance.pitch = 1.0;
            utterance.volume = 1.0;

            utterance.onstart = () => console.log('✅ Simple test started');
            utterance.onend = () => console.log('✅ Simple test completed');
            utterance.onerror = (e) => console.error('❌ Simple test failed:', e);

            window.speechSynthesis.speak(utterance);
          }}
          title="Test simple TTS"
        >
          🔊 TTS
        </button>
      </div>

      <div className="chat-messages">
        {messages.map(message => (
          <div key={message.id} className={`message ${message.sender}-message`}>
            <div className="message-content">{message.text}</div>
            <span className="message-timestamp">{message.timestamp}</span>
          </div>
        ))}
        {isThinking && (
          <div className="thinking-indicator">
            <div className="thinking-dot"></div>
            <div className="thinking-dot"></div>
            <div className="thinking-dot"></div>
          </div>
        )}
        <div ref={messagesEndRef} />
      </div>
      <div className="chat-input-container">
        <input
          type="text"
          id="user-input"
          value={input}
          onChange={(e) => setInput(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder={currentLanguage === 'kannada' ? "ನಿಮ್ಮ ಸಂದೇಶವನ್ನು ಟೈಪ್ ಮಾಡಿ..." : "Type your message..."}
          disabled={isThinking}
        />
        <button
          id="send-button"
          onClick={sendMessage}
          disabled={isThinking}
        >
          {currentLanguage === 'kannada' ? 'ಕಳುಹಿಸಿ' : 'Send'}
        </button>
      </div>
    </div>
  );
};

export default Chatbot;
