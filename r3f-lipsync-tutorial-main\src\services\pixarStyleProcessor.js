// Pixar Style Image Processor
// Converts uploaded images to Pixar/cartoon style and creates avatar textures

class PixarStyleProcessor {
  constructor() {
    this.initialized = false;
  }

  async initialize() {
    if (this.initialized) return;
    this.initialized = true;
    console.log('Pixar Style Processor initialized');
  }

  async processImageToPixarStyle(imageFile) {
    if (!this.initialized) {
      await this.initialize();
    }

    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = async () => {
        try {
          // Set canvas size
          canvas.width = 512;
          canvas.height = 512;

          // Draw original image
          ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

          // Apply Pixar-style filters
          const pixarCanvas = await this.applyPixarStyleFilters(canvas);

          // Create face-optimized texture
          const faceTexture = this.createFaceTexture(pixarCanvas);

          // Convert to data URL
          const pixarTexture = faceTexture.toDataURL('image/jpeg', 0.9);

          resolve({
            original: img.src,
            pixarStyle: pixarTexture,
            processed: true
          });
        } catch (error) {
          reject(error);
        }
      };

      img.onerror = () => reject(new Error('Failed to load image'));
      img.src = URL.createObjectURL(imageFile);
    });
  }

  async applyPixarStyleFilters(canvas) {
    const ctx = canvas.getContext('2d');
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const data = imageData.data;

    // Apply multiple filters to achieve Pixar-like appearance
    this.applySmoothingFilter(data, canvas.width, canvas.height);
    this.enhanceColors(data);
    this.applyCartoonEffect(data, canvas.width, canvas.height);
    this.increaseSaturation(data);
    this.adjustBrightness(data);

    // Create new canvas with processed data
    const processedCanvas = document.createElement('canvas');
    processedCanvas.width = canvas.width;
    processedCanvas.height = canvas.height;
    const processedCtx = processedCanvas.getContext('2d');
    
    processedCtx.putImageData(imageData, 0, 0);
    
    // Apply additional canvas-based effects
    await this.applyGlowEffect(processedCtx, canvas.width, canvas.height);
    
    return processedCanvas;
  }

  applySmoothingFilter(data, width, height) {
    // Bilateral filter for smoothing while preserving edges
    const smoothed = new Uint8ClampedArray(data);
    const radius = 3;
    
    for (let y = radius; y < height - radius; y++) {
      for (let x = radius; x < width - radius; x++) {
        const idx = (y * width + x) * 4;
        
        let r = 0, g = 0, b = 0, totalWeight = 0;
        
        // Sample surrounding pixels
        for (let dy = -radius; dy <= radius; dy++) {
          for (let dx = -radius; dx <= radius; dx++) {
            const sampleIdx = ((y + dy) * width + (x + dx)) * 4;
            const distance = Math.sqrt(dx * dx + dy * dy);
            const weight = Math.exp(-distance / 2);
            
            r += data[sampleIdx] * weight;
            g += data[sampleIdx + 1] * weight;
            b += data[sampleIdx + 2] * weight;
            totalWeight += weight;
          }
        }
        
        smoothed[idx] = r / totalWeight;
        smoothed[idx + 1] = g / totalWeight;
        smoothed[idx + 2] = b / totalWeight;
      }
    }
    
    // Copy smoothed data back
    for (let i = 0; i < data.length; i += 4) {
      data[i] = smoothed[i];
      data[i + 1] = smoothed[i + 1];
      data[i + 2] = smoothed[i + 2];
    }
  }

  enhanceColors(data) {
    // Enhance colors to be more vibrant like Pixar style
    for (let i = 0; i < data.length; i += 4) {
      // Convert to HSL for better color manipulation
      const hsl = this.rgbToHsl(data[i], data[i + 1], data[i + 2]);
      
      // Increase saturation and adjust lightness
      hsl[1] = Math.min(1, hsl[1] * 1.4); // Increase saturation
      hsl[2] = Math.max(0.1, Math.min(0.9, hsl[2] * 1.1)); // Slight brightness boost
      
      // Convert back to RGB
      const rgb = this.hslToRgb(hsl[0], hsl[1], hsl[2]);
      data[i] = rgb[0];
      data[i + 1] = rgb[1];
      data[i + 2] = rgb[2];
    }
  }

  applyCartoonEffect(data, width, height) {
    // Quantize colors to create cartoon-like appearance
    const levels = 6; // Number of color levels
    
    for (let i = 0; i < data.length; i += 4) {
      // Quantize each color channel
      data[i] = Math.round(data[i] / 255 * levels) * (255 / levels);
      data[i + 1] = Math.round(data[i + 1] / 255 * levels) * (255 / levels);
      data[i + 2] = Math.round(data[i + 2] / 255 * levels) * (255 / levels);
    }
  }

  increaseSaturation(data) {
    // Further increase saturation for Pixar-like vibrancy
    for (let i = 0; i < data.length; i += 4) {
      const r = data[i] / 255;
      const g = data[i + 1] / 255;
      const b = data[i + 2] / 255;
      
      // Calculate luminance
      const luminance = 0.299 * r + 0.587 * g + 0.114 * b;
      
      // Increase saturation
      const saturationBoost = 1.3;
      data[i] = Math.min(255, (r - luminance) * saturationBoost + luminance * 255);
      data[i + 1] = Math.min(255, (g - luminance) * saturationBoost + luminance * 255);
      data[i + 2] = Math.min(255, (b - luminance) * saturationBoost + luminance * 255);
    }
  }

  adjustBrightness(data) {
    // Adjust brightness and contrast for Pixar-like appearance
    const brightness = 1.1;
    const contrast = 1.2;
    
    for (let i = 0; i < data.length; i += 4) {
      // Apply brightness and contrast
      data[i] = Math.min(255, Math.max(0, (data[i] - 128) * contrast + 128 + brightness * 10));
      data[i + 1] = Math.min(255, Math.max(0, (data[i + 1] - 128) * contrast + 128 + brightness * 10));
      data[i + 2] = Math.min(255, Math.max(0, (data[i + 2] - 128) * contrast + 128 + brightness * 10));
    }
  }

  async applyGlowEffect(ctx, width, height) {
    // Add subtle glow effect
    ctx.globalCompositeOperation = 'screen';
    ctx.filter = 'blur(2px)';
    ctx.globalAlpha = 0.3;
    ctx.drawImage(ctx.canvas, 0, 0);
    
    // Reset composite operation
    ctx.globalCompositeOperation = 'source-over';
    ctx.filter = 'none';
    ctx.globalAlpha = 1.0;
  }

  createFaceTexture(processedCanvas) {
    // Create optimized face texture for avatar
    const faceCanvas = document.createElement('canvas');
    const ctx = faceCanvas.getContext('2d');
    
    faceCanvas.width = 512;
    faceCanvas.height = 512;
    
    // Enable high-quality scaling
    ctx.imageSmoothingEnabled = true;
    ctx.imageSmoothingQuality = 'high';
    
    // Draw processed image with face-optimized cropping
    const size = Math.min(processedCanvas.width, processedCanvas.height);
    const x = (processedCanvas.width - size) / 2;
    const y = (processedCanvas.height - size) / 4; // Position face higher
    
    ctx.drawImage(
      processedCanvas,
      x, y, size, size * 0.8,
      50, 50, 412, 412
    );
    
    // Apply face mask
    this.applyFaceMask(ctx, 512, 512);
    
    return faceCanvas;
  }

  applyFaceMask(ctx, width, height) {
    // Create oval face mask
    const imageData = ctx.getImageData(0, 0, width, height);
    const data = imageData.data;
    
    const centerX = width / 2;
    const centerY = height / 2;
    const radiusX = width * 0.35;
    const radiusY = height * 0.4;
    
    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        const index = (y * width + x) * 4;
        
        const dx = (x - centerX) / radiusX;
        const dy = (y - centerY) / radiusY;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        let alpha = 1.0;
        if (distance > 0.8) {
          alpha = Math.max(0, 1 - (distance - 0.8) / 0.2);
          alpha = alpha * alpha; // Smooth falloff
        }
        
        data[index + 3] = Math.min(data[index + 3], alpha * 255);
      }
    }
    
    ctx.putImageData(imageData, 0, 0);
  }

  // Utility functions for color space conversion
  rgbToHsl(r, g, b) {
    r /= 255; g /= 255; b /= 255;
    const max = Math.max(r, g, b), min = Math.min(r, g, b);
    let h, s, l = (max + min) / 2;

    if (max === min) {
      h = s = 0;
    } else {
      const d = max - min;
      s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
      switch (max) {
        case r: h = (g - b) / d + (g < b ? 6 : 0); break;
        case g: h = (b - r) / d + 2; break;
        case b: h = (r - g) / d + 4; break;
      }
      h /= 6;
    }
    return [h, s, l];
  }

  hslToRgb(h, s, l) {
    let r, g, b;
    if (s === 0) {
      r = g = b = l;
    } else {
      const hue2rgb = (p, q, t) => {
        if (t < 0) t += 1;
        if (t > 1) t -= 1;
        if (t < 1/6) return p + (q - p) * 6 * t;
        if (t < 1/2) return q;
        if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
        return p;
      };
      const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
      const p = 2 * l - q;
      r = hue2rgb(p, q, h + 1/3);
      g = hue2rgb(p, q, h);
      b = hue2rgb(p, q, h - 1/3);
    }
    return [Math.round(r * 255), Math.round(g * 255), Math.round(b * 255)];
  }
}

// Export singleton instance
export const pixarStyleProcessor = new PixarStyleProcessor();
