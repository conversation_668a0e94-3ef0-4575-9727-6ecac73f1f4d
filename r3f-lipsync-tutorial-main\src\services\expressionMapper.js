import * as THREE from 'three';

/**
 * Expression Mapper Service
 * Maps emotions and sentiments to facial expressions and animations
 */
class ExpressionMapper {
  constructor() {
    this.currentExpression = 'neutral';
    this.expressionIntensity = 0.5;
    this.transitionSpeed = 0.1;
    
    // Define expression mappings
    this.expressions = {
      neutral: {
        eyeBrowInner: 0,
        eyeBrowOuter: 0,
        eyeSquint: 0,
        eyeWide: 0,
        cheekPuff: 0,
        cheekSquint: 0,
        noseSneer: 0,
        mouthSmile: 0,
        mouthFrown: 0,
        mouthPucker: 0,
        jawOpen: 0
      },
      happy: {
        eyeBrowInner: 0,
        eyeBrowOuter: 0,
        eyeSquint: 0.3,
        eyeWide: 0,
        cheekPuff: 0.2,
        cheekSquint: 0.4,
        noseSneer: 0,
        mouthSmile: 0.8,
        mouthFrown: 0,
        mouthPucker: 0,
        jawOpen: 0.1
      },
      sad: {
        eyeBrowInner: 0.6,
        eyeBrowOuter: -0.3,
        eyeSquint: 0,
        eyeWide: 0,
        cheekPuff: 0,
        cheekSquint: 0,
        noseSneer: 0,
        mouthSmile: 0,
        mouthFrown: 0.7,
        mouthPucker: 0,
        jawOpen: 0
      },
      angry: {
        eyeBrowInner: -0.8,
        eyeBrowOuter: -0.6,
        eyeSquint: 0.7,
        eyeWide: 0,
        cheekPuff: 0,
        cheekSquint: 0.3,
        noseSneer: 0.5,
        mouthSmile: 0,
        mouthFrown: 0.4,
        mouthPucker: 0,
        jawOpen: 0
      },
      surprised: {
        eyeBrowInner: 0.8,
        eyeBrowOuter: 0.8,
        eyeSquint: 0,
        eyeWide: 0.9,
        cheekPuff: 0,
        cheekSquint: 0,
        noseSneer: 0,
        mouthSmile: 0,
        mouthFrown: 0,
        mouthPucker: 0,
        jawOpen: 0.6
      },
      excited: {
        eyeBrowInner: 0.4,
        eyeBrowOuter: 0.3,
        eyeSquint: 0.2,
        eyeWide: 0.3,
        cheekPuff: 0.3,
        cheekSquint: 0.5,
        noseSneer: 0,
        mouthSmile: 0.9,
        mouthFrown: 0,
        mouthPucker: 0,
        jawOpen: 0.2
      },
      thinking: {
        eyeBrowInner: 0.3,
        eyeBrowOuter: 0,
        eyeSquint: 0.4,
        eyeWide: 0,
        cheekPuff: 0,
        cheekSquint: 0.2,
        noseSneer: 0,
        mouthSmile: 0,
        mouthFrown: 0,
        mouthPucker: 0.3,
        jawOpen: 0
      },
      confused: {
        eyeBrowInner: 0.5,
        eyeBrowOuter: -0.2,
        eyeSquint: 0.3,
        eyeWide: 0,
        cheekPuff: 0,
        cheekSquint: 0,
        noseSneer: 0,
        mouthSmile: 0,
        mouthFrown: 0.2,
        mouthPucker: 0.2,
        jawOpen: 0
      }
    };

    // Sentiment analysis keywords
    this.sentimentKeywords = {
      happy: ['happy', 'joy', 'great', 'excellent', 'wonderful', 'fantastic', 'amazing', 'love', 'good', 'nice', 'awesome', 'brilliant'],
      sad: ['sad', 'sorry', 'unfortunate', 'bad', 'terrible', 'awful', 'disappointed', 'upset', 'down', 'depressed'],
      angry: ['angry', 'mad', 'furious', 'annoyed', 'irritated', 'frustrated', 'outraged', 'livid'],
      surprised: ['surprised', 'wow', 'amazing', 'incredible', 'unbelievable', 'shocking', 'astonishing'],
      excited: ['excited', 'thrilled', 'pumped', 'enthusiastic', 'eager', 'energetic', 'hyped'],
      thinking: ['think', 'consider', 'perhaps', 'maybe', 'might', 'could', 'wondering', 'pondering'],
      confused: ['confused', 'puzzled', 'unclear', 'uncertain', 'bewildered', 'perplexed', 'lost']
    };
  }

  /**
   * Analyze text sentiment and return appropriate expression
   */
  analyzeSentiment(text) {
    const lowerText = text.toLowerCase();
    let maxScore = 0;
    let detectedEmotion = 'neutral';

    // Check each emotion category
    Object.entries(this.sentimentKeywords).forEach(([emotion, keywords]) => {
      let score = 0;
      keywords.forEach(keyword => {
        const regex = new RegExp(`\\b${keyword}\\b`, 'gi');
        const matches = lowerText.match(regex);
        if (matches) {
          score += matches.length;
        }
      });

      if (score > maxScore) {
        maxScore = score;
        detectedEmotion = emotion;
      }
    });

    // Calculate intensity based on score
    this.expressionIntensity = Math.min(1.0, maxScore * 0.3 + 0.3);
    
    console.log(`Detected emotion: ${detectedEmotion} with intensity: ${this.expressionIntensity}`);
    return detectedEmotion;
  }

  /**
   * Set expression with smooth transition
   */
  setExpression(emotionName, intensity = null) {
    if (this.expressions[emotionName]) {
      this.currentExpression = emotionName;
      if (intensity !== null) {
        this.expressionIntensity = Math.max(0, Math.min(1, intensity));
      }
    }
  }

  /**
   * Apply expression to a 3D face mesh
   */
  applyExpressionToMesh(mesh, deltaTime) {
    if (!mesh || !mesh.morphTargetDictionary || !mesh.morphTargetInfluences) {
      return;
    }

    const targetExpression = this.expressions[this.currentExpression];
    if (!targetExpression) return;

    // Apply each expression component with smooth transitions
    Object.entries(targetExpression).forEach(([expressionType, targetValue]) => {
      const morphTargetName = this.getMorphTargetName(expressionType);
      const index = mesh.morphTargetDictionary[morphTargetName];
      
      if (index !== undefined) {
        const currentValue = mesh.morphTargetInfluences[index];
        const adjustedTarget = targetValue * this.expressionIntensity;
        
        // Smooth transition
        mesh.morphTargetInfluences[index] = THREE.MathUtils.lerp(
          currentValue,
          adjustedTarget,
          this.transitionSpeed
        );
      }
    });
  }

  /**
   * Map expression types to morphTarget names
   */
  getMorphTargetName(expressionType) {
    const mapping = {
      eyeBrowInner: 'browInnerUp',
      eyeBrowOuter: 'browOuterUp',
      eyeSquint: 'eyeSquint',
      eyeWide: 'eyeWide',
      cheekPuff: 'cheekPuff',
      cheekSquint: 'cheekSquint',
      noseSneer: 'noseSneer',
      mouthSmile: 'mouthSmile',
      mouthFrown: 'mouthFrown',
      mouthPucker: 'mouthPucker',
      jawOpen: 'jawOpen'
    };

    return mapping[expressionType] || expressionType;
  }

  /**
   * Get current expression state
   */
  getCurrentExpression() {
    return {
      name: this.currentExpression,
      intensity: this.expressionIntensity,
      values: this.expressions[this.currentExpression]
    };
  }

  /**
   * Reset to neutral expression
   */
  resetExpression() {
    this.currentExpression = 'neutral';
    this.expressionIntensity = 0.5;
  }
}

// Export singleton instance
export const expressionMapper = new ExpressionMapper();
export default ExpressionMapper;
