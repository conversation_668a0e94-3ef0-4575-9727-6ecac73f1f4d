import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { pixarStyleProcessor } from '../services/pixarStyleProcessor';

const PixarUpload = ({ onPixarAvatarCreated }) => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [preview, setPreview] = useState(null);
  const [error, setError] = useState(null);
  const [progress, setProgress] = useState(0);

  const onDrop = useCallback(async (acceptedFiles) => {
    const file = acceptedFiles[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      setError('Please upload an image file');
      return;
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      setError('Image file is too large. Please choose a file under 10MB.');
      return;
    }

    setError(null);
    setIsProcessing(true);
    setProgress(0);

    try {
      // Create preview
      const previewUrl = URL.createObjectURL(file);
      setPreview(previewUrl);
      setProgress(20);

      console.log('Starting Pixar-style processing...');
      
      // Simulate processing steps for better UX
      setProgress(40);
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setProgress(60);
      const result = await pixarStyleProcessor.processImageToPixarStyle(file);
      
      setProgress(80);
      await new Promise(resolve => setTimeout(resolve, 300));
      
      setProgress(100);
      console.log('Pixar processing complete:', result);

      if (onPixarAvatarCreated) {
        onPixarAvatarCreated({
          type: 'pixar',
          data: result,
          timestamp: Date.now()
        });
      }

      console.log('Pixar avatar created successfully');
    } catch (error) {
      console.error('Error processing image:', error);
      setError(error.message || 'Failed to process image. Please try another image.');
    } finally {
      setIsProcessing(false);
      setProgress(0);
    }
  }, [onPixarAvatarCreated]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.bmp', '.webp']
    },
    multiple: false,
    disabled: isProcessing
  });

  return (
    <div style={{
      position: 'fixed',
      top: '20px',
      left: '20px',
      width: '300px',
      background: 'rgba(255, 255, 255, 0.95)',
      borderRadius: '15px',
      padding: '20px',
      boxShadow: '0 8px 32px rgba(0, 0, 0, 0.2)',
      zIndex: 1000,
      backdropFilter: 'blur(10px)',
      border: '1px solid rgba(255, 255, 255, 0.3)'
    }}>
      <h3 style={{
        margin: '0 0 15px 0',
        color: '#333',
        fontSize: '18px',
        fontWeight: 'bold',
        textAlign: 'center'
      }}>
        🎨 Pixar Avatar Creator
      </h3>

      <div
        {...getRootProps()}
        style={{
          border: `2px dashed ${isDragActive ? '#007bff' : '#ddd'}`,
          borderRadius: '10px',
          padding: '30px 20px',
          textAlign: 'center',
          cursor: isProcessing ? 'not-allowed' : 'pointer',
          background: isDragActive ? 'rgba(0, 123, 255, 0.1)' : 'rgba(248, 249, 250, 0.8)',
          transition: 'all 0.3s ease',
          marginBottom: '15px'
        }}
      >
        <input {...getInputProps()} />
        
        {isProcessing ? (
          <div>
            <div style={{
              fontSize: '24px',
              marginBottom: '10px',
              animation: 'spin 2s linear infinite'
            }}>
              🎭
            </div>
            <p style={{ margin: '0 0 10px 0', color: '#666', fontSize: '14px' }}>
              Creating Pixar-style avatar...
            </p>
            <div style={{
              width: '100%',
              height: '6px',
              background: '#e0e0e0',
              borderRadius: '3px',
              overflow: 'hidden'
            }}>
              <div style={{
                width: `${progress}%`,
                height: '100%',
                background: 'linear-gradient(90deg, #ff6b6b, #4ecdc4)',
                borderRadius: '3px',
                transition: 'width 0.3s ease'
              }} />
            </div>
            <p style={{ margin: '5px 0 0 0', color: '#999', fontSize: '12px' }}>
              {progress}% complete
            </p>
          </div>
        ) : (
          <div>
            <div style={{ fontSize: '32px', marginBottom: '10px' }}>
              {isDragActive ? '🎨' : '📸'}
            </div>
            <p style={{ margin: '0 0 5px 0', color: '#333', fontSize: '14px', fontWeight: '500' }}>
              {isDragActive ? 'Drop your photo here!' : 'Upload a photo'}
            </p>
            <p style={{ margin: '0', color: '#666', fontSize: '12px' }}>
              Drag & drop or click to select
            </p>
          </div>
        )}
      </div>

      {preview && !isProcessing && (
        <div style={{
          textAlign: 'center',
          marginBottom: '15px'
        }}>
          <img
            src={preview}
            alt="Preview"
            style={{
              width: '80px',
              height: '80px',
              borderRadius: '10px',
              objectFit: 'cover',
              border: '2px solid #007bff'
            }}
          />
          <p style={{ margin: '5px 0 0 0', color: '#28a745', fontSize: '12px', fontWeight: '500' }}>
            ✅ Pixar avatar ready!
          </p>
        </div>
      )}

      {error && (
        <div style={{
          background: 'rgba(220, 53, 69, 0.1)',
          border: '1px solid rgba(220, 53, 69, 0.3)',
          borderRadius: '8px',
          padding: '10px',
          marginBottom: '15px'
        }}>
          <p style={{ margin: '0', color: '#dc3545', fontSize: '12px' }}>
            ❌ {error}
          </p>
        </div>
      )}

      <div style={{
        background: 'rgba(0, 123, 255, 0.1)',
        borderRadius: '8px',
        padding: '12px',
        marginTop: '10px'
      }}>
        <p style={{ margin: '0 0 8px 0', color: '#007bff', fontSize: '13px', fontWeight: '500' }}>
          ✨ Pixar Magic Features:
        </p>
        <ul style={{ margin: '0', paddingLeft: '16px', color: '#666', fontSize: '11px' }}>
          <li>Cartoon-style color enhancement</li>
          <li>Smooth skin and vibrant colors</li>
          <li>Pixar-like glow effects</li>
          <li>Perfect face fitting</li>
        </ul>
      </div>

      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};

export default PixarUpload;
