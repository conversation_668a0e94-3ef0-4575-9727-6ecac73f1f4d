.ui-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1000;
}

.ui-overlay > * {
  pointer-events: auto;
}

/* Avatar Name Styling */
.avatar-name-container {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 1001;
}

.avatar-name {
  background-color: rgba(255, 255, 255, 0.9);
  padding: 8px 15px;
  border-radius: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.avatar-name:hover {
  background-color: rgba(255, 255, 255, 1);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.avatar-name span {
  font-weight: 500;
  color: #333;
  margin-right: 8px;
}

.edit-name-btn {
  background: none;
  border: none;
  color: #3a7bd5;
  cursor: pointer;
  font-size: 14px;
  opacity: 0.6;
  transition: opacity 0.2s;
}

.avatar-name:hover .edit-name-btn {
  opacity: 1;
}

.name-input-container {
  display: flex;
  align-items: center;
}

.name-input-container input {
  border: none;
  outline: none;
  background: transparent;
  padding: 5px;
  font-size: 14px;
  width: 150px;
}

.name-input-container button {
  background: none;
  border: none;
  color: #3a7bd5;
  cursor: pointer;
  font-size: 14px;
  margin-left: 5px;
  transition: color 0.2s;
}

.name-input-container button:hover {
  color: #2d5a9c;
}

.chatbot-container {
  position: absolute;
  bottom: 20px;
  right: 20px;
  width: 350px;
  height: 500px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Language Toggle Styles */
.language-toggle {
  display: flex;
  padding: 10px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 10px 10px 0 0;
  gap: 5px;
  justify-content: center;
}

.lang-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.lang-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.lang-btn.active {
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.lang-btn.active:hover {
  background: rgba(255, 255, 255, 1);
}

/* Language Notification */
.language-notification {
  position: absolute;
  top: 50px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  z-index: 1000;
  animation: fadeInOut 2s ease-in-out;
}

@keyframes fadeInOut {
  0% { opacity: 0; transform: translateX(-50%) translateY(-10px); }
  20% { opacity: 1; transform: translateX(-50%) translateY(0); }
  80% { opacity: 1; transform: translateX(-50%) translateY(0); }
  100% { opacity: 0; transform: translateX(-50%) translateY(-10px); }
}

/* Debug TTS Button */
.debug-tts-btn {
  position: absolute;
  top: 10px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 4px 8px;
  border-radius: 15px;
  cursor: pointer;
  font-size: 10px;
  font-weight: 500;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  margin-left: 5px;
}

.debug-tts-btn:first-of-type {
  right: 60px;
}

.debug-tts-btn:last-of-type {
  right: 10px;
}

.debug-tts-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.chat-messages {
  flex: 1;
  padding: 15px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.message {
  margin-bottom: 10px;
  max-width: 80%;
  padding: 10px 15px;
  border-radius: 18px;
  position: relative;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.user-message {
  align-self: flex-end;
  background-color: #3a7bd5;
  color: white;
  border-bottom-right-radius: 5px;
}

.bot-message {
  align-self: flex-start;
  background-color: #e1f0ff;
  color: #333;
  border-bottom-left-radius: 5px;
}

.message-content {
  word-wrap: break-word;
}

.message-timestamp {
  font-size: 0.7rem;
  color: rgba(0, 0, 0, 0.5);
  position: absolute;
  bottom: -18px;
  right: 5px;
}

.user-message .message-timestamp {
  color: rgba(255, 255, 255, 0.7);
}

.chat-input-container {
  display: flex;
  padding: 10px;
  border-top: 1px solid #e0e0e0;
  background-color: white;
}

#user-input {
  flex: 1;
  padding: 10px 15px;
  border: 1px solid #ddd;
  border-radius: 20px;
  outline: none;
  font-size: 14px;
}

#user-input:focus {
  border-color: #3a7bd5;
}

#send-button {
  margin-left: 10px;
  padding: 10px 15px;
  background-color: #3a7bd5;
  color: white;
  border: none;
  border-radius: 20px;
  cursor: pointer;
  transition: background-color 0.2s;
}

#send-button:hover {
  background-color: #2d5a9c;
}

#send-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.thinking-indicator {
  display: flex;
  align-self: flex-start;
  background-color: #e1f0ff;
  padding: 15px;
  border-radius: 18px;
  margin-bottom: 10px;
}

.thinking-dot {
  width: 8px;
  height: 8px;
  margin: 0 3px;
  background-color: #3a7bd5;
  border-radius: 50%;
  animation: thinking 1.4s infinite ease-in-out both;
}

.thinking-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.thinking-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes thinking {
  0%, 80%, 100% { transform: scale(0); }
  40% { transform: scale(1); }
}

/* Image Upload Styles */
.image-upload-container {
  margin-bottom: 20px;
}

.upload-section {
  margin-bottom: 15px;
}

.dropzone {
  border: 2px dashed #ccc;
  border-radius: 10px;
  padding: 30px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
}

.dropzone:hover {
  border-color: #007bff;
  background: rgba(0, 123, 255, 0.1);
}

.dropzone.active {
  border-color: #007bff;
  background: rgba(0, 123, 255, 0.2);
  transform: scale(1.02);
}

.dropzone.processing {
  border-color: #ffc107;
  background: rgba(255, 193, 7, 0.1);
  cursor: not-allowed;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.upload-icon {
  font-size: 48px;
  opacity: 0.7;
}

.upload-text {
  font-size: 16px;
  font-weight: 500;
  margin: 0;
  color: #333;
}

.upload-hint {
  color: #666;
  font-size: 12px;
}

.processing-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  background: rgba(220, 53, 69, 0.1);
  border: 1px solid #dc3545;
  border-radius: 5px;
  color: #dc3545;
  margin-top: 10px;
}

.error-icon {
  font-size: 18px;
}

.error-close {
  background: none;
  border: none;
  color: #dc3545;
  cursor: pointer;
  font-size: 18px;
  margin-left: auto;
}

.preview-section {
  margin-top: 15px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  backdrop-filter: blur(10px);
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.preview-header h4 {
  margin: 0;
  color: #333;
}

.clear-preview {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #666;
}

.preview-image {
  width: 100%;
  max-width: 200px;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.upload-tips {
  background: rgba(255, 255, 255, 0.05);
  padding: 15px;
  border-radius: 10px;
  backdrop-filter: blur(10px);
}

.upload-tips h4 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 14px;
}

.upload-tips ul {
  margin: 0;
  padding-left: 20px;
  font-size: 12px;
  color: #666;
}

.upload-tips li {
  margin-bottom: 5px;
}

/* Avatar Selection Styles */
.avatar-selection {
  margin-bottom: 20px;
}

.avatar-options {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.avatar-option {
  flex: 1;
  padding: 10px;
  border: 2px solid transparent;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.avatar-option:hover {
  border-color: #007bff;
  background: rgba(0, 123, 255, 0.1);
}

.avatar-option.active {
  border-color: #007bff;
  background: rgba(0, 123, 255, 0.2);
}

.avatar-option-icon {
  font-size: 24px;
  margin-bottom: 5px;
}

.avatar-option-text {
  font-size: 12px;
  color: #333;
  font-weight: 500;
}

.custom-avatar-preview {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  margin: 0 auto 5px;
  display: block;
}
