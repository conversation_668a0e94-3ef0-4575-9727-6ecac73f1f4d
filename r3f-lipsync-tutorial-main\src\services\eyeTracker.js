import * as THREE from 'three';

/**
 * Eye Tracking and Animation Service
 * Handles eye movement, blinking, and gaze direction
 */
class EyeTracker {
  constructor() {
    this.isEnabled = true;
    this.blinkEnabled = true;
    this.gazeEnabled = true;
    
    // Blinking parameters
    this.blinkInterval = 3000; // Average time between blinks (ms)
    this.blinkVariation = 2000; // Random variation in blink timing
    this.blinkDuration = 150; // Duration of a blink
    this.lastBlinkTime = 0;
    this.nextBlinkTime = this.calculateNextBlinkTime();
    this.isBlinking = false;
    this.blinkProgress = 0;
    
    // Gaze parameters
    this.gazeTarget = new THREE.Vector3(0, 0, 1);
    this.currentGaze = new THREE.Vector3(0, 0, 1);
    this.gazeSpeed = 0.02;
    this.gazeRange = 0.3; // How far eyes can move
    this.gazeIdleMovement = true;
    this.lastGazeChange = 0;
    this.gazeChangeInterval = 5000; // Change gaze every 5 seconds
    
    // Eye movement patterns
    this.gazePatterns = {
      idle: [
        { x: 0, y: 0, duration: 3000 },
        { x: 0.2, y: 0.1, duration: 2000 },
        { x: -0.1, y: 0.2, duration: 2500 },
        { x: 0.1, y: -0.1, duration: 2000 },
        { x: -0.2, y: 0, duration: 1500 }
      ],
      speaking: [
        { x: 0, y: 0, duration: 1000 },
        { x: 0.1, y: 0.05, duration: 800 },
        { x: -0.05, y: 0.1, duration: 900 },
        { x: 0, y: 0, duration: 700 }
      ],
      listening: [
        { x: 0, y: 0, duration: 2000 },
        { x: 0.15, y: 0, duration: 1500 },
        { x: 0, y: 0.1, duration: 1200 },
        { x: -0.1, y: 0, duration: 1800 }
      ]
    };
    
    this.currentPattern = 'idle';
    this.patternIndex = 0;
    this.patternStartTime = 0;
  }

  /**
   * Calculate next blink time with natural variation
   */
  calculateNextBlinkTime() {
    return Date.now() + this.blinkInterval + (Math.random() - 0.5) * this.blinkVariation;
  }

  /**
   * Update eye animations
   */
  update(deltaTime, isSpeaking = false, isListening = false) {
    if (!this.isEnabled) return;

    const currentTime = Date.now();
    
    // Update gaze pattern based on state
    if (isSpeaking && this.currentPattern !== 'speaking') {
      this.setGazePattern('speaking');
    } else if (isListening && this.currentPattern !== 'listening') {
      this.setGazePattern('listening');
    } else if (!isSpeaking && !isListening && this.currentPattern !== 'idle') {
      this.setGazePattern('idle');
    }

    // Update blinking
    if (this.blinkEnabled) {
      this.updateBlinking(currentTime);
    }

    // Update gaze
    if (this.gazeEnabled) {
      this.updateGaze(currentTime, deltaTime);
    }
  }

  /**
   * Update blinking animation
   */
  updateBlinking(currentTime) {
    if (!this.isBlinking && currentTime >= this.nextBlinkTime) {
      // Start a new blink
      this.isBlinking = true;
      this.blinkProgress = 0;
      this.lastBlinkTime = currentTime;
    }

    if (this.isBlinking) {
      const elapsed = currentTime - this.lastBlinkTime;
      this.blinkProgress = elapsed / this.blinkDuration;

      if (this.blinkProgress >= 1) {
        // Blink complete
        this.isBlinking = false;
        this.blinkProgress = 0;
        this.nextBlinkTime = this.calculateNextBlinkTime();
      }
    }
  }

  /**
   * Update gaze direction
   */
  updateGaze(currentTime, deltaTime) {
    // Update gaze pattern
    if (this.gazeIdleMovement) {
      this.updateGazePattern(currentTime);
    }

    // Smoothly move current gaze toward target
    this.currentGaze.lerp(this.gazeTarget, this.gazeSpeed);
  }

  /**
   * Update gaze pattern based on current behavior
   */
  updateGazePattern(currentTime) {
    const pattern = this.gazePatterns[this.currentPattern];
    if (!pattern || pattern.length === 0) return;

    const currentStep = pattern[this.patternIndex];
    const elapsed = currentTime - this.patternStartTime;

    if (elapsed >= currentStep.duration) {
      // Move to next step in pattern
      this.patternIndex = (this.patternIndex + 1) % pattern.length;
      this.patternStartTime = currentTime;
      
      const nextStep = pattern[this.patternIndex];
      this.gazeTarget.set(
        nextStep.x * this.gazeRange,
        nextStep.y * this.gazeRange,
        1
      );
    }
  }

  /**
   * Set gaze pattern
   */
  setGazePattern(patternName) {
    if (this.gazePatterns[patternName]) {
      this.currentPattern = patternName;
      this.patternIndex = 0;
      this.patternStartTime = Date.now();
    }
  }

  /**
   * Apply eye animations to mesh
   */
  applyToMesh(mesh) {
    if (!mesh || !mesh.morphTargetDictionary || !mesh.morphTargetInfluences) {
      return;
    }

    // Apply blinking
    if (this.blinkEnabled && this.isBlinking) {
      const blinkValue = this.getBlinkValue();
      
      // Apply to both eyes
      const leftBlinkIndex = mesh.morphTargetDictionary['eyeBlinkLeft'];
      const rightBlinkIndex = mesh.morphTargetDictionary['eyeBlinkRight'];
      
      if (leftBlinkIndex !== undefined) {
        mesh.morphTargetInfluences[leftBlinkIndex] = blinkValue;
      }
      if (rightBlinkIndex !== undefined) {
        mesh.morphTargetInfluences[rightBlinkIndex] = blinkValue;
      }
    }

    // Apply gaze direction (if mesh supports eye movement)
    if (this.gazeEnabled) {
      this.applyGazeToMesh(mesh);
    }
  }

  /**
   * Calculate blink value based on progress
   */
  getBlinkValue() {
    if (!this.isBlinking) return 0;
    
    // Create a smooth blink curve
    const t = this.blinkProgress;
    if (t < 0.5) {
      // Closing phase
      return Math.sin(t * Math.PI);
    } else {
      // Opening phase
      return Math.sin((1 - t) * Math.PI);
    }
  }

  /**
   * Apply gaze direction to mesh (for advanced eye rigs)
   */
  applyGazeToMesh(mesh) {
    // This would be used for more advanced eye rigs with look-at controls
    // For now, we'll use basic eye movement morphTargets if available
    
    const eyeLookUpIndex = mesh.morphTargetDictionary['eyeLookUp'];
    const eyeLookDownIndex = mesh.morphTargetDictionary['eyeLookDown'];
    const eyeLookLeftIndex = mesh.morphTargetDictionary['eyeLookLeft'];
    const eyeLookRightIndex = mesh.morphTargetDictionary['eyeLookRight'];

    // Apply vertical gaze
    if (this.currentGaze.y > 0 && eyeLookUpIndex !== undefined) {
      mesh.morphTargetInfluences[eyeLookUpIndex] = Math.abs(this.currentGaze.y);
    } else if (this.currentGaze.y < 0 && eyeLookDownIndex !== undefined) {
      mesh.morphTargetInfluences[eyeLookDownIndex] = Math.abs(this.currentGaze.y);
    }

    // Apply horizontal gaze
    if (this.currentGaze.x > 0 && eyeLookRightIndex !== undefined) {
      mesh.morphTargetInfluences[eyeLookRightIndex] = Math.abs(this.currentGaze.x);
    } else if (this.currentGaze.x < 0 && eyeLookLeftIndex !== undefined) {
      mesh.morphTargetInfluences[eyeLookLeftIndex] = Math.abs(this.currentGaze.x);
    }
  }

  /**
   * Manually trigger a blink
   */
  triggerBlink() {
    if (!this.isBlinking) {
      this.isBlinking = true;
      this.blinkProgress = 0;
      this.lastBlinkTime = Date.now();
    }
  }

  /**
   * Set gaze target manually
   */
  setGazeTarget(x, y, z = 1) {
    this.gazeTarget.set(
      Math.max(-this.gazeRange, Math.min(this.gazeRange, x)),
      Math.max(-this.gazeRange, Math.min(this.gazeRange, y)),
      z
    );
    this.gazeIdleMovement = false;
  }

  /**
   * Enable/disable idle gaze movement
   */
  setIdleGazeMovement(enabled) {
    this.gazeIdleMovement = enabled;
    if (enabled) {
      this.setGazePattern(this.currentPattern);
    }
  }

  /**
   * Get current eye state
   */
  getEyeState() {
    return {
      isBlinking: this.isBlinking,
      blinkProgress: this.blinkProgress,
      gazeDirection: this.currentGaze.clone(),
      gazeTarget: this.gazeTarget.clone(),
      currentPattern: this.currentPattern
    };
  }
}

// Export singleton instance
export const eyeTracker = new EyeTracker();
export default EyeTracker;
