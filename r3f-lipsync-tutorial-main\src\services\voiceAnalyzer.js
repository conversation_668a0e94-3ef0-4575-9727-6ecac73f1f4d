/**
 * Real-time Voice Analysis Service
 * Analyzes audio input and generates visemes for lip-sync
 */
class VoiceAnalyzer {
  constructor() {
    this.isInitialized = false;
    this.isAnalyzing = false;
    this.audioContext = null;
    this.analyser = null;
    this.microphone = null;
    this.dataArray = null;
    this.bufferLength = 0;
    
    // Frequency analysis parameters
    this.fftSize = 2048;
    this.smoothingTimeConstant = 0.8;
    
    // Viseme detection parameters
    this.frequencyBands = {
      low: { min: 0, max: 250 },      // Low frequencies (vowels)
      mid: { min: 250, max: 2000 },   // Mid frequencies (consonants)
      high: { min: 2000, max: 8000 }  // High frequencies (fricatives)
    };
    
    // Current analysis results
    this.currentViseme = 'viseme_PP';
    this.visemeIntensity = 0;
    this.volumeLevel = 0;
    this.isSpeaking = false;
    this.speakingThreshold = 0.01;
    
    // Viseme mapping based on frequency analysis
    this.visemeMapping = {
      silence: 'viseme_PP',
      lowVowel: 'viseme_AA',    // A, O sounds
      midVowel: 'viseme_I',     // I, <PERSON> sounds  
      highVowel: 'viseme_U',    // U, OO sounds
      bilabial: 'viseme_PP',    // P, B, M sounds
      labiodental: 'viseme_FF', // F, V sounds
      dental: 'viseme_TH',      // TH sounds
      alveolar: 'viseme_kk'     // T, D, S, Z sounds
    };
    
    // Callbacks
    this.onVisemeChange = null;
    this.onVolumeChange = null;
    this.onSpeakingStateChange = null;
  }

  /**
   * Initialize the voice analyzer
   */
  async initialize() {
    if (this.isInitialized) return true;

    try {
      // Request microphone access
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        } 
      });

      // Create audio context
      this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
      
      // Create analyser node
      this.analyser = this.audioContext.createAnalyser();
      this.analyser.fftSize = this.fftSize;
      this.analyser.smoothingTimeConstant = this.smoothingTimeConstant;
      
      // Connect microphone to analyser
      this.microphone = this.audioContext.createMediaStreamSource(stream);
      this.microphone.connect(this.analyser);
      
      // Set up data array for frequency analysis
      this.bufferLength = this.analyser.frequencyBinCount;
      this.dataArray = new Uint8Array(this.bufferLength);
      
      this.isInitialized = true;
      console.log('Voice analyzer initialized successfully');
      return true;
      
    } catch (error) {
      console.error('Failed to initialize voice analyzer:', error);
      return false;
    }
  }

  /**
   * Start real-time voice analysis
   */
  startAnalysis() {
    if (!this.isInitialized) {
      console.warn('Voice analyzer not initialized');
      return false;
    }

    this.isAnalyzing = true;
    this.analyzeAudio();
    console.log('Voice analysis started');
    return true;
  }

  /**
   * Stop voice analysis
   */
  stopAnalysis() {
    this.isAnalyzing = false;
    console.log('Voice analysis stopped');
  }

  /**
   * Main audio analysis loop
   */
  analyzeAudio() {
    if (!this.isAnalyzing) return;

    // Get frequency data
    this.analyser.getByteFrequencyData(this.dataArray);
    
    // Calculate volume level
    this.calculateVolumeLevel();
    
    // Detect speaking state
    this.detectSpeakingState();
    
    // Analyze frequencies and determine viseme
    if (this.isSpeaking) {
      this.analyzeFrequenciesForViseme();
    } else {
      this.currentViseme = 'viseme_PP';
      this.visemeIntensity = 0;
    }
    
    // Continue analysis
    requestAnimationFrame(() => this.analyzeAudio());
  }

  /**
   * Calculate overall volume level
   */
  calculateVolumeLevel() {
    let sum = 0;
    for (let i = 0; i < this.bufferLength; i++) {
      sum += this.dataArray[i];
    }
    
    this.volumeLevel = sum / this.bufferLength / 255;
    
    if (this.onVolumeChange) {
      this.onVolumeChange(this.volumeLevel);
    }
  }

  /**
   * Detect if user is speaking
   */
  detectSpeakingState() {
    const wasSpeaking = this.isSpeaking;
    this.isSpeaking = this.volumeLevel > this.speakingThreshold;
    
    if (wasSpeaking !== this.isSpeaking && this.onSpeakingStateChange) {
      this.onSpeakingStateChange(this.isSpeaking);
    }
  }

  /**
   * Analyze frequency bands to determine appropriate viseme
   */
  analyzeFrequenciesForViseme() {
    const nyquist = this.audioContext.sampleRate / 2;
    const binWidth = nyquist / this.bufferLength;
    
    // Calculate energy in different frequency bands
    const bandEnergies = {
      low: 0,
      mid: 0,
      high: 0
    };
    
    for (let i = 0; i < this.bufferLength; i++) {
      const frequency = i * binWidth;
      const energy = this.dataArray[i] / 255;
      
      if (frequency >= this.frequencyBands.low.min && frequency <= this.frequencyBands.low.max) {
        bandEnergies.low += energy;
      } else if (frequency >= this.frequencyBands.mid.min && frequency <= this.frequencyBands.mid.max) {
        bandEnergies.mid += energy;
      } else if (frequency >= this.frequencyBands.high.min && frequency <= this.frequencyBands.high.max) {
        bandEnergies.high += energy;
      }
    }
    
    // Determine viseme based on frequency distribution
    const newViseme = this.determineVisemeFromBands(bandEnergies);
    const newIntensity = Math.min(1.0, this.volumeLevel * 3);
    
    if (newViseme !== this.currentViseme) {
      this.currentViseme = newViseme;
      if (this.onVisemeChange) {
        this.onVisemeChange(this.currentViseme, newIntensity);
      }
    }
    
    this.visemeIntensity = newIntensity;
  }

  /**
   * Determine viseme based on frequency band analysis
   */
  determineVisemeFromBands(bandEnergies) {
    const total = bandEnergies.low + bandEnergies.mid + bandEnergies.high;
    if (total < 0.1) return 'viseme_PP'; // Silence
    
    // Normalize energies
    const normalized = {
      low: bandEnergies.low / total,
      mid: bandEnergies.mid / total,
      high: bandEnergies.high / total
    };
    
    // Determine viseme based on dominant frequencies
    if (normalized.low > 0.6) {
      // Low frequency dominant - likely vowels
      if (normalized.mid > 0.2) {
        return 'viseme_AA'; // Open vowels (A, O)
      } else {
        return 'viseme_U'; // Closed vowels (U, OO)
      }
    } else if (normalized.mid > 0.5) {
      // Mid frequency dominant
      if (normalized.high > 0.3) {
        return 'viseme_I'; // Front vowels (I, E)
      } else {
        return 'viseme_kk'; // Consonants (T, D, K, G)
      }
    } else if (normalized.high > 0.4) {
      // High frequency dominant - fricatives
      if (normalized.mid > 0.3) {
        return 'viseme_FF'; // Labiodental (F, V)
      } else {
        return 'viseme_TH'; // Dental (TH, S, Z)
      }
    }
    
    // Default to bilabial
    return 'viseme_PP';
  }

  /**
   * Get current analysis state
   */
  getCurrentState() {
    return {
      isAnalyzing: this.isAnalyzing,
      isSpeaking: this.isSpeaking,
      currentViseme: this.currentViseme,
      visemeIntensity: this.visemeIntensity,
      volumeLevel: this.volumeLevel
    };
  }

  /**
   * Set callback for viseme changes
   */
  setVisemeCallback(callback) {
    this.onVisemeChange = callback;
  }

  /**
   * Set callback for volume changes
   */
  setVolumeCallback(callback) {
    this.onVolumeChange = callback;
  }

  /**
   * Set callback for speaking state changes
   */
  setSpeakingStateCallback(callback) {
    this.onSpeakingStateChange = callback;
  }

  /**
   * Cleanup resources
   */
  cleanup() {
    this.stopAnalysis();
    
    if (this.microphone) {
      this.microphone.disconnect();
    }
    
    if (this.audioContext) {
      this.audioContext.close();
    }
    
    this.isInitialized = false;
  }
}

// Export singleton instance
export const voiceAnalyzer = new VoiceAnalyzer();
export default VoiceAnalyzer;
