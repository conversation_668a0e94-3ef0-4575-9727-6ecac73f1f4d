# 🇮🇳 Kannada Language Support

The chatbot now supports **Kannada language** responses! You can interact with your avatar in both English and Kannada.

## ✨ Features

### 🗣️ **Bilingual Chatbot**
- **English Support**: Default language with full conversational AI
- **Kannada Support**: Native Kannada responses using Kannada script (ಕನ್ನಡ)
- **Auto-Detection**: Automatically detects Kannada input and responds in Kannada
- **Language Toggle**: Easy switching between English and Kannada

### 🎭 **Avatar Integration**
- **Lip-Sync**: Full lip-sync support for both English and Kannada speech
- **Voice Synthesis**: Text-to-speech works with Kannada text
- **Animations**: All avatar animations work with Kannada responses
- **Expressions**: Emotion-based expressions work in both languages

## 🚀 How to Use

### 1. **Language Toggle Buttons**
- Look for the language toggle at the top of the chatbot
- **🇺🇸 EN**: Switch to English mode
- **🇮🇳 ಕನ್ನಡ**: Switch to Kannada mode
- **Visual Feedback**: Shows notification when language is switched

### 2. **Automatic Language Detection**
- Type in Kannada script and the bot will automatically respond in Kannada
- Type in English and get English responses
- **Audio Support**: <PERSON>tar speaks in appropriate language/accent

### 3. **Language Switch Commands**
You can also use voice commands to switch languages:

**English Commands:**
- "Speak in Kannada"
- "Reply in Kannada"
- "Talk in English"

**Kannada Commands:**
- "ಕನ್ನಡದಲ್ಲಿ ಮಾತನಾಡು"
- "ಇಂಗ್ಲಿಷ್ದಲ್ಲಿ ಉತ್ತರಿಸು"

## 🎵 **Audio & Voice Features**

### **Kannada Text-to-Speech (TTS)**
The avatar now supports Kannada audio with intelligent voice selection:

#### **Voice Priority System:**
1. **🎯 Native Kannada Voices**: Uses actual Kannada TTS if available
2. **🇮🇳 Hindi Voices**: Falls back to Hindi for similar pronunciation
3. **🌏 Other Indian Voices**: Uses Tamil, Telugu, or Bengali voices
4. **🔄 English Fallback**: Uses English with optimized parameters for Kannada

#### **Audio Parameters:**
- **Kannada Mode**: Slower speech rate (0.9x) for clarity
- **Fallback Mode**: Much slower rate (0.7x) with higher pitch for better pronunciation
- **Console Logging**: Shows which voice is being used in browser console

### **Testing Audio Support:**
1. **Open Browser Console** (F12) to see voice selection logs
2. **Switch to Kannada mode** using the 🇮🇳 ಕನ್ನಡ button
3. **Type Kannada text** like "ನಮಸ್ಕಾರ" and listen to the response
4. **Check console** for voice information:
   - "Found Kannada voice: [Voice Name]" - Best case
   - "Using Hindi voice as fallback: [Voice Name]" - Good fallback
   - "Using English with Kannada-optimized parameters" - Basic fallback

## 📝 **Sample Kannada Phrases to Try**

### **Greetings (ನಮಸ್ಕಾರಗಳು)**
- `ನಮಸ್ಕಾರ` - Hello/Namaste
- `ಹಲೋ` - Hello
- `ಶುಭೋದಯ` - Good morning
- `ಶುಭ ಸಂಜೆ` - Good evening
- `ಹೇಗಿದ್ದೀರಿ?` - How are you?

### **Common Questions (ಸಾಮಾನ್ಯ ಪ್ರಶ್ನೆಗಳು)**
- `ನಿಮ್ಮ ಹೆಸರೇನು?` - What is your name?
- `ಇಂದು ಏನು ದಿನಾಂಕ?` - What is today's date?
- `ಸಮಯ ಎಷ್ಟಾಯಿತು?` - What time is it?
- `ನೀವು ಏನು ಮಾಡಬಲ್ಲಿರಿ?` - What can you do?
- `ಈ ಪ್ರಾಜೆಕ್ಟ್ ಬಗ್ಗೆ ಹೇಳಿ` - Tell me about this project

### **Conversation Starters (ಸಂಭಾಷಣೆ ಪ್ರಾರಂಭಿಕರು)**
- `ಕನ್ನಡದಲ್ಲಿ ಮಾತನಾಡೋಣ` - Let's talk in Kannada
- `ನಿಮಗೆ ಕನ್ನಡ ಬರುತ್ತದೆಯೇ?` - Do you know Kannada?
- `ಬೆಂಗಳೂರು ಬಗ್ಗೆ ಹೇಳಿ` - Tell me about Bangalore
- `ಕರ್ನಾಟಕ ಸಂಸ್ಕೃತಿ ಬಗ್ಗೆ ಮಾತನಾಡಿ` - Talk about Karnataka culture

## 🎯 **Language Features**

### **English Mode**
- Greeting: "Hey there!"
- Natural conversational responses
- Full project knowledge integration
- Error messages in English

### **Kannada Mode**
- Greeting: "ನಮಸ್ಕಾರ!"
- Pure Kannada responses (no English mixing)
- Cultural context awareness
- Error messages in Kannada

## 🔧 **Technical Implementation**

### **Language Detection**
- **Unicode Range**: Detects Kannada characters (U+0C80-U+0CFF)
- **Auto-Switch**: Automatically switches to Kannada mode for Kannada input
- **Persistent State**: Remembers language preference during session

### **AI Response Generation**
- **Separate Prompts**: Different system prompts for English and Kannada
- **Cultural Context**: Kannada responses include cultural awareness
- **Script Purity**: Kannada mode uses only Kannada script, no English mixing

### **Error Handling**
- **Bilingual Errors**: Error messages in appropriate language
- **Rate Limiting**: Rate limit messages in both languages
- **Fallback**: Graceful fallback for API issues

## 🎨 **UI Enhancements**

### **Language Toggle**
- **Visual Indicators**: Flag emojis and native script
- **Active State**: Clear indication of current language
- **Smooth Transitions**: Animated button states

### **Localized Interface**
- **Input Placeholder**: Changes based on selected language
- **Button Text**: "Send" becomes "ಕಳುಹಿಸಿ" in Kannada mode
- **Tooltips**: Language-appropriate help text

## 🌟 **Cultural Features**

### **Kannada Context Awareness**
- Understanding of Karnataka culture
- Knowledge of Kannada literature and history
- Awareness of local customs and traditions
- Appropriate use of respectful language

### **Regional Sensitivity**
- Respectful communication style
- Cultural context in responses
- Local knowledge integration
- Appropriate greetings and farewells

## 🎭 **Avatar Behavior**

### **Language-Aware Animations**
- **Greeting Animation**: Triggered by both English and Kannada greetings
- **Expression Mapping**: Emotion detection works in both languages
- **Lip-Sync**: Accurate mouth movements for Kannada phonemes

### **Voice Synthesis**
- **Text-to-Speech**: Supports Kannada text rendering
- **Pronunciation**: Proper Kannada pronunciation when available
- **Fallback**: Graceful handling when Kannada TTS is not available

## 🚀 **Getting Started**

1. **Start the application** at `http://localhost:5173/`
2. **Look for the language toggle** at the top of the chatbot
3. **Click "🇮🇳 ಕನ್ನಡ"** to switch to Kannada mode
4. **Type "ನಮಸ್ಕಾರ"** to get started with a Kannada greeting
5. **Enjoy bilingual conversations** with your avatar!

## 📚 **Learning Resources**

### **Kannada Script Basics**
- Kannada uses its own script derived from Brahmi
- Written left to right
- 49 letters including vowels and consonants
- Rich literary tradition spanning over 1000 years

### **Common Phrases for Testing**
Try these phrases to test the Kannada functionality:
- `ಧನ್ಯವಾದಗಳು` - Thank you
- `ಕ್ಷಮಿಸಿ` - Sorry/Excuse me
- `ಅರ್ಥವಾಗಲಿಲ್ಲ` - I don't understand
- `ಮತ್ತೆ ಹೇಳಿ` - Say again
- `ಸರಿ` - Okay/Correct

Experience the rich culture of Karnataka through bilingual conversations with your avatar! 🎭🇮🇳
